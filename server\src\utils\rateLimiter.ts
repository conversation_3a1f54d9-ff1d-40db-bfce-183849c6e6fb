import { logger } from './logger';

export interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerDay: number;
  maxTokensPerMinute: number;
  retryDelayMs: number;
  maxRetries: number;
}

export class AIRateLimiter {
  private static instance: AIRateLimiter;
  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();
  private dailyCounts: Map<string, { count: number; resetTime: number }> = new Map();
  private tokenCounts: Map<string, { tokens: number; resetTime: number }> = new Map();
  private lastRequestTime: number = 0;
  private minRequestInterval: number = 0;

  private constructor(private config: RateLimitConfig) {
    this.minRequestInterval = (60 * 1000) / config.maxRequestsPerMinute;
  }

  public static getInstance(config?: RateLimitConfig): AIRateLimiter {
    if (!AIRateLimiter.instance) {
      AIRateLimiter.instance = new AIRateLimiter(config || {
        maxRequestsPerMinute: 25,
        maxRequestsPerDay: 180,
        maxTokensPerMinute: 240000,
        retryDelayMs: 2000,
        maxRetries: 3
      });
    }
    return AIRateLimiter.instance;
  }

  public async checkRateLimit(modelName: string, estimatedTokens: number = 1000): Promise<void> {
    const now = Date.now();
    const minuteKey = `${modelName}:minute:${Math.floor(now / (60 * 1000))}`;
    const dayKey = `${modelName}:day:${Math.floor(now / (24 * 60 * 60 * 1000))}`;
    const tokenKey = `${modelName}:tokens:${Math.floor(now / (60 * 1000))}`;

    const dailyData = this.dailyCounts.get(dayKey) || { count: 0, resetTime: now + (24 * 60 * 60 * 1000) };
    if (dailyData.count >= this.config.maxRequestsPerDay) {
      const waitTime = dailyData.resetTime - now;
      logger.warn(`Daily rate limit exceeded for ${modelName}, waiting ${Math.ceil(waitTime / 1000)}s`);
      await this.delay(waitTime);
    }

    const minuteData = this.requestCounts.get(minuteKey) || { count: 0, resetTime: now + (60 * 1000) };
    if (minuteData.count >= this.config.maxRequestsPerMinute) {
      const waitTime = minuteData.resetTime - now;
      logger.warn(`Minute rate limit exceeded for ${modelName}, waiting ${Math.ceil(waitTime / 1000)}s`);
      await this.delay(waitTime);
    }

    const tokenData = this.tokenCounts.get(tokenKey) || { tokens: 0, resetTime: now + (60 * 1000) };
    if (tokenData.tokens + estimatedTokens > this.config.maxTokensPerMinute) {
      const waitTime = tokenData.resetTime - now;
      logger.warn(`Token rate limit exceeded for ${modelName}, waiting ${Math.ceil(waitTime / 1000)}s`);
      await this.delay(waitTime);
    }

    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      await this.delay(waitTime);
    }

    this.requestCounts.set(minuteKey, { count: minuteData.count + 1, resetTime: minuteData.resetTime });
    this.dailyCounts.set(dayKey, { count: dailyData.count + 1, resetTime: dailyData.resetTime });
    this.tokenCounts.set(tokenKey, { tokens: tokenData.tokens + estimatedTokens, resetTime: tokenData.resetTime });
    this.lastRequestTime = Date.now();

    logger.debug(`Rate limit check passed for ${modelName}`, {
      minuteCount: minuteData.count + 1,
      dailyCount: dailyData.count + 1,
      tokenCount: tokenData.tokens + estimatedTokens
    });
  }

  public async withRetry<T>(
    operation: () => Promise<T>,
    modelName: string,
    estimatedTokens: number = 1000
  ): Promise<T> {
    let lastError: Error;
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        await this.checkRateLimit(modelName, estimatedTokens);
        return await operation();
      } catch (error: any) {
        lastError = error;
        if (error.message?.includes('429') || error.message?.includes('Too Many Requests')) {
          const retryDelay = this.config.retryDelayMs * Math.pow(2, attempt - 1);
          logger.warn(`Rate limit hit, retrying in ${retryDelay}ms (attempt ${attempt}/${this.config.maxRetries})`);
          await this.delay(retryDelay);
          continue;
        }
        throw error;
      }
    }
    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getUsageStats(modelName: string): {
    minuteRequests: number;
    dailyRequests: number;
    minuteTokens: number;
    limits: RateLimitConfig;
  } {
    const now = Date.now();
    const minuteKey = `${modelName}:minute:${Math.floor(now / (60 * 1000))}`;
    const dayKey = `${modelName}:day:${Math.floor(now / (24 * 60 * 60 * 1000))}`;
    const tokenKey = `${modelName}:tokens:${Math.floor(now / (60 * 1000))}`;

    const minuteData = this.requestCounts.get(minuteKey) || { count: 0, resetTime: 0 };
    const dailyData = this.dailyCounts.get(dayKey) || { count: 0, resetTime: 0 };
    const tokenData = this.tokenCounts.get(tokenKey) || { tokens: 0, resetTime: 0 };

    return {
      minuteRequests: minuteData.count,
      dailyRequests: dailyData.count,
      minuteTokens: tokenData.tokens,
      limits: this.config
    };
  }
} 