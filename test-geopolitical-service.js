// Test the GeopoliticalAnalysisService directly
const path = require('path');

// Mock the logger to avoid import issues
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error
};

// Import the compiled service
const { GeopoliticalAnalysisService } = require('./server/dist/services/GeopoliticalAnalysisService');

const testArticles = [
  {
    title: "Iran accuses US of 'waging war' under 'absurd pretext'",
    content: "Iran has accused the United States of waging war under an absurd pretext following recent military actions in the region. The Iranian government has threatened to retaliate against what it calls unprovoked aggression. Oil markets are watching closely as tensions escalate in the Middle East.",
    source: "Reuters",
    publishedAt: new Date(),
    category: "world",
    url: "https://example.com/1"
  },
  {
    title: "U.S. calls on China to prevent Iran from closing Strait of Hormuz",
    content: "The United States has called on China to use its influence to prevent Iran from closing the strategic Strait of Hormuz shipping lane. The strait is a critical chokepoint for global oil supplies, with approximately 20% of the world's petroleum passing through daily. Any closure would have severe implications for global energy markets.",
    source: "Bloomberg",
    publishedAt: new Date(),
    category: "world",
    url: "https://example.com/2"
  },
  {
    title: "The US military used a 30,000-pound bunker-buster bomb in Middle East operation",
    content: "The United States military deployed a massive 30,000-pound bunker-buster bomb in a recent Middle East operation. The GBU-57 Massive Ordnance Penetrator is designed to destroy deeply buried targets and represents a significant escalation in military capabilities. Defense contractors who manufacture such weapons are seeing increased interest.",
    source: "Defense News",
    publishedAt: new Date(),
    category: "world",
    url: "https://example.com/3"
  }
];

async function testGeopoliticalService() {
  console.log('🌍 Testing GeopoliticalAnalysisService');
  console.log('=' .repeat(60));

  try {
    const geopoliticalService = GeopoliticalAnalysisService.getInstance();
    
    for (let i = 0; i < testArticles.length; i++) {
      const article = testArticles[i];
      console.log(`\n📰 Test ${i + 1}: ${article.title.substring(0, 60)}...`);
      console.log('-'.repeat(50));
      
      const analysis = geopoliticalService.analyzeGeopoliticalEvent(article);
      
      if (analysis) {
        console.log('✅ GEOPOLITICAL EVENT DETECTED!');
        console.log(`   Event Type: ${analysis.event.type}`);
        console.log(`   Severity: ${analysis.event.severity}`);
        console.log(`   Region: ${analysis.event.region}`);
        console.log(`   Key Entities: ${analysis.event.keyEntities.join(', ')}`);
        console.log(`   Overall Market Impact: ${analysis.overallMarketImpact}`);
        console.log(`   Analysis Confidence: ${analysis.confidence}%`);
        console.log(`   Affected Sectors: ${analysis.sectorMappings.length}`);
        
        if (analysis.sectorMappings.length > 0) {
          console.log('\n   📊 Sector Mappings:');
          analysis.sectorMappings.forEach(mapping => {
            console.log(`     • ${mapping.sector}: ${mapping.impactDirection} impact (${mapping.confidence}% confidence)`);
            console.log(`       Impact Type: ${mapping.impactType}`);
            console.log(`       Affected Tickers: ${mapping.affectedTickers.join(', ')}`);
            console.log(`       Reasoning: ${mapping.reasoning.substring(0, 150)}...`);
          });
        }
        
        if (analysis.tradingImplications.length > 0) {
          console.log('\n   💼 Trading Implications:');
          analysis.tradingImplications.forEach(implication => {
            console.log(`     • ${implication}`);
          });
        }
        
        console.log(`\n   📝 Analysis Reasoning: ${analysis.reasoning.substring(0, 200)}...`);
        
      } else {
        console.log('❌ No geopolitical event detected');
        console.log('   This indicates a problem with pattern matching or event classification');
      }
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 GeopoliticalAnalysisService Test Complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testGeopoliticalService();
