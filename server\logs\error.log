[2025-06-23 17:46:54.982] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 17:46:56.215] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 23:40:26.456] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 23:42:10.224] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-24 00:18:33.905] [ERROR] [General]: Error analyzing market impact: article.publishedAt.toISOString is not a function | {"service":"trading-bot","stack":"TypeError: article.publishedAt.toISOString is not a function\n    at NewsImpactAnalysisService.buildAnalysisPrompt (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:69:34)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:26:33)\n    at C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:295:45\n    at Array.map (<anonymous>)\n    at NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:294:41)\n    at C:\\work\\Trading Bot\\server\\dist\\routes\\discovery.js:103:58\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[2025-06-24 00:21:43.680] [ERROR] [General]: Error analyzing market impact: article.publishedAt.toISOString is not a function | {"service":"trading-bot","stack":"TypeError: article.publishedAt.toISOString is not a function\n    at NewsImpactAnalysisService.buildAnalysisPrompt (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:91:34)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:26:33)\n    at C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:317:45\n    at Array.map (<anonymous>)\n    at NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:316:41)\n    at C:\\work\\Trading Bot\\server\\dist\\routes\\discovery.js:140:58\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[2025-06-24 00:34:47.051] [ERROR] [General]: 🤖 AI model error | {service=trading-bot, title=Stunning and mysterious 'red sprite' lightning storm captured in photos - Earth.com, error=[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"16s"}], stack=Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"16s"}]
    at _makeRequestInternal (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:353:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async generateContent (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:752:22)
    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:34:26)
    at async C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:333:34
    at async Promise.all (index 0)
    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:336:34)
    at async C:\work\Trading Bot\server\dist\routes\discovery.js:37:34}
[2025-06-24 00:34:47.057] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"16s"}] | {service=trading-bot, status=429, statusText=Too Many Requests, errorDetails=[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"16s"}], stack=Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"16s"}]
    at _makeRequestInternal (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:353:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async generateContent (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:752:22)
    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:34:26)
    at async C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:333:34
    at async Promise.all (index 0)
    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:336:34)
    at async C:\work\Trading Bot\server\dist\routes\discovery.js:37:34}
[2025-06-24 00:36:00.988] [ERROR] [General]: 🤖 AI model error | {service=trading-bot, title=U.S. used 14 bunker-busters, 7 B-2 bombers in "Midnight Hammer" strikes on Iran - Axios, error=[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"2s"}], stack=Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"2s"}]
    at _makeRequestInternal (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:353:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async generateContent (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:752:22)
    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:34:26)
    at async C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:333:34
    at async Promise.all (index 2)
    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:336:34)
    at async C:\work\Trading Bot\server\dist\routes\discovery.js:37:34}
[2025-06-24 00:36:00.993] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"2s"}] | {service=trading-bot, status=429, statusText=Too Many Requests, errorDetails=[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"2s"}], stack=Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"2s"}]
    at _makeRequestInternal (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:353:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async generateContent (C:\work\Trading Bot\server\node_modules\@google\generative-ai\dist\index.js:752:22)
    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:34:26)
    at async C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:333:34
    at async Promise.all (index 2)
    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\work\Trading Bot\server\dist\services\NewsImpactAnalysisService.js:336:34)
    at async C:\work\Trading Bot\server\dist\routes\discovery.js:37:34}
