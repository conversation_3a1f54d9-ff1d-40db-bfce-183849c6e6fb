import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '../utils/logger';
import { GlobalNewsArticle } from './GlobalNewsService';
import { SETTINGS } from '../config/settings';

export interface AIRelevanceAnalysis {
  isMarketRelevant: boolean;
  relevanceScore: number; // 0-1
  confidence: number; // 0-1
  reasoning: string;
  eventType: 'geopolitical' | 'economic' | 'corporate' | 'regulatory' | 'technological' | 'other';
  impactLevel: 'low' | 'medium' | 'high';
  affectedSectors: string[];
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
}

export class AINewsRelevanceService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor(apiKey: string) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: SETTINGS.llmModel });
  }

  /**
   * Stage 1: Quick header analysis to determine if article has potential market impact
   */
  public async analyzeHeaderRelevance(title: string, source: string): Promise<{
    isRelevant: boolean;
    confidence: number;
    reasoning: string;
  }> {
    const startTime = Date.now();

    try {
      logger.info('🤖 Starting AI header analysis', {
        title: title.substring(0, 100),
        source,
        timestamp: new Date().toISOString()
      });

      const prompt = `
Analyze this news headline for potential financial market impact:

HEADLINE: "${title}"
SOURCE: ${source}

Determine if this headline suggests content that could impact financial markets, stock prices, or trading decisions.

Consider:
- Geopolitical events (conflicts, sanctions, diplomatic changes)
- Economic policy changes (interest rates, regulations, trade)
- Major corporate events (mergers, bankruptcies, leadership changes)
- Technological breakthroughs or disruptions
- Natural disasters or supply chain disruptions
- Regulatory changes affecting industries

Respond in JSON format DO NOT RETURN RESPONSE WITH \`\`\`json prefix or suffix.
{
  "isRelevant": boolean,
  "confidence": number (0-1),
  "reasoning": "Brief explanation of why this headline is/isn't market relevant"
}
`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      const processingTime = Date.now() - startTime;

      logger.info('🤖 AI header analysis API call completed', {
        title: title.substring(0, 100),
        processingTime: `${processingTime}ms`,
        responseLength: response.length
      });

      try {
        // Clean the response by removing markdown code blocks
        let cleanResponse = response.trim();

        // Remove markdown code blocks more aggressively
        cleanResponse = cleanResponse.replace(/^```json\s*/gm, '').replace(/^```\s*/gm, '').replace(/\s*```$/gm, '');

        // Find JSON content between braces
        const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          cleanResponse = jsonMatch[0];
        }

        const analysis = JSON.parse(cleanResponse);

        logger.info('✅ AI header analysis successful', {
          title: title.substring(0, 100),
          isRelevant: analysis.isRelevant,
          confidence: analysis.confidence,
          reasoning: analysis.reasoning.substring(0, 200),
          processingTime: `${processingTime}ms`
        });

        return {
          isRelevant: analysis.isRelevant,
          confidence: analysis.confidence,
          reasoning: analysis.reasoning
        };
      } catch (parseError) {
        logger.warn('❌ Failed to parse AI header analysis response', {
          title: title.substring(0, 100),
          response: response.substring(0, 500),
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        });
        return {
          isRelevant: false,
          confidence: 0,
          reasoning: 'Failed to parse AI response'
        };
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('❌ Error in AI header analysis:', {
        title: title.substring(0, 100),
        source,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${processingTime}ms`
      });
      return {
        isRelevant: false,
        confidence: 0,
        reasoning: 'AI analysis failed'
      };
    }
  }

  /**
   * Stage 2: Full article analysis for detailed market impact assessment
   */
  public async analyzeFullArticleRelevance(article: GlobalNewsArticle): Promise<AIRelevanceAnalysis> {
    const startTime = Date.now();

    try {
      logger.info('🔍 Starting AI full article analysis', {
        title: article.title.substring(0, 100),
        source: article.source,
        category: article.category,
        contentLength: article.content.length,
        timestamp: new Date().toISOString()
      });

      const prompt = `
Analyze this news article for comprehensive financial market impact:

TITLE: "${article.title}"
CONTENT: "${article.content}"
SOURCE: ${article.source}
CATEGORY: ${article.category}

Provide a detailed analysis of how this article could impact financial markets:

1. Market Relevance: Does this have potential to move markets?
2. Event Classification: What type of event is this?
3. Impact Assessment: How significant could the market impact be?
4. Sector Analysis: Which market sectors could be affected?
5. Timing: What's the likely timeframe for market impact?

Focus on:
- Direct market implications (price movements, volatility)
- Sector-specific impacts (energy, defense, tech, healthcare, etc.)
- Geopolitical implications for global markets
- Supply chain or economic policy effects
- Corporate performance implications

Respond in JSON format:
{
  "isMarketRelevant": boolean,
  "relevanceScore": number (0-1, where 1 is extremely market relevant),
  "confidence": number (0-1, confidence in this analysis),
  "reasoning": "Detailed explanation of market relevance and potential impacts",
  "eventType": "geopolitical|economic|corporate|regulatory|technological|other",
  "impactLevel": "low|medium|high",
  "affectedSectors": ["array", "of", "sector", "names"],
  "timeframe": "immediate|short_term|medium_term|long_term"
}
`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      const processingTime = Date.now() - startTime;

      logger.info('🔍 AI full article analysis API call completed', {
        title: article.title.substring(0, 100),
        processingTime: `${processingTime}ms`,
        responseLength: response.length
      });

      try {
        // Clean the response by removing markdown code blocks
        let cleanResponse = response.trim();
        if (cleanResponse.startsWith('```json')) {
          cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanResponse.startsWith('```')) {
          cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        const analysis = JSON.parse(cleanResponse);

        logger.info('✅ AI full article analysis successful', {
          title: article.title.substring(0, 100),
          isMarketRelevant: analysis.isMarketRelevant,
          relevanceScore: analysis.relevanceScore,
          confidence: analysis.confidence,
          eventType: analysis.eventType,
          impactLevel: analysis.impactLevel,
          sectorsCount: analysis.affectedSectors?.length || 0,
          affectedSectors: analysis.affectedSectors,
          timeframe: analysis.timeframe,
          processingTime: `${processingTime}ms`
        });

        return {
          isMarketRelevant: analysis.isMarketRelevant,
          relevanceScore: analysis.relevanceScore,
          confidence: analysis.confidence,
          reasoning: analysis.reasoning,
          eventType: analysis.eventType,
          impactLevel: analysis.impactLevel,
          affectedSectors: analysis.affectedSectors || [],
          timeframe: analysis.timeframe
        };
      } catch (parseError) {
        logger.warn('❌ Failed to parse AI full analysis response', {
          title: article.title.substring(0, 100),
          response: response.substring(0, 500),
          parseError: parseError instanceof Error ? parseError.message : 'Unknown parse error',
          processingTime: `${processingTime}ms`
        });
        return this.getDefaultAnalysis();
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('❌ Error in AI full article analysis:', {
        title: article.title.substring(0, 100),
        source: article.source,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${processingTime}ms`
      });
      return this.getDefaultAnalysis();
    }
  }

  /**
   * Batch analyze multiple articles with rate limiting
   */
  public async batchAnalyzeHeaders(
    articles: { title: string; source: string }[],
    batchSize: number = 5
  ): Promise<Array<{ isRelevant: boolean; confidence: number; reasoning: string }>> {
    const results = [];
    
    for (let i = 0; i < articles.length; i += batchSize) {
      const batch = articles.slice(i, i + batchSize);
      const batchPromises = batch.map(article => 
        this.analyzeHeaderRelevance(article.title, article.source)
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Rate limiting - wait between batches
      if (i + batchSize < articles.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }

  /**
   * Filter articles using two-stage AI analysis
   */
  public async filterArticlesWithAI(
    articles: GlobalNewsArticle[],
    headerThreshold: number = 0.3,
    fullAnalysisThreshold: number = 0.5
  ): Promise<{
    relevantArticles: GlobalNewsArticle[];
    analysisResults: AIRelevanceAnalysis[];
    stats: {
      totalArticles: number;
      passedHeaderAnalysis: number;
      passedFullAnalysis: number;
      finalRelevantArticles: number;
    };
  }> {
    logger.info('Starting two-stage AI filtering', {
      totalArticles: articles.length,
      headerThreshold,
      fullAnalysisThreshold
    });

    // Stage 1: Header analysis
    const headerAnalyses = await this.batchAnalyzeHeaders(
      articles.map(a => ({ title: a.title, source: a.source }))
    );

    const passedHeaderAnalysis = articles.filter((_, index) => 
      headerAnalyses[index].isRelevant && headerAnalyses[index].confidence >= headerThreshold
    );

    logger.info('Stage 1 (Header Analysis) completed', {
      totalArticles: articles.length,
      passedHeaderAnalysis: passedHeaderAnalysis.length,
      filterRate: `${((passedHeaderAnalysis.length / articles.length) * 100).toFixed(1)}%`
    });

    // Stage 2: Full analysis for articles that passed header analysis
    const fullAnalysisResults: AIRelevanceAnalysis[] = [];
    const relevantArticles: GlobalNewsArticle[] = [];

    for (const article of passedHeaderAnalysis) {
      const fullAnalysis = await this.analyzeFullArticleRelevance(article);
      fullAnalysisResults.push(fullAnalysis);

      if (fullAnalysis.isMarketRelevant && fullAnalysis.relevanceScore >= fullAnalysisThreshold) {
        relevantArticles.push({
          ...article,
          relevanceScore: fullAnalysis.relevanceScore
        });
      }

      // Rate limiting between full analyses
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const stats = {
      totalArticles: articles.length,
      passedHeaderAnalysis: passedHeaderAnalysis.length,
      passedFullAnalysis: fullAnalysisResults.filter(r => r.isMarketRelevant).length,
      finalRelevantArticles: relevantArticles.length
    };

    logger.info('Two-stage AI filtering completed', stats);

    return {
      relevantArticles,
      analysisResults: fullAnalysisResults,
      stats
    };
  }

  private getDefaultAnalysis(): AIRelevanceAnalysis {
    return {
      isMarketRelevant: false,
      relevanceScore: 0,
      confidence: 0,
      reasoning: 'Analysis failed',
      eventType: 'other',
      impactLevel: 'low',
      affectedSectors: [],
      timeframe: 'long_term'
    };
  }
}
