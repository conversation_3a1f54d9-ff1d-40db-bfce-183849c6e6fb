// Test the AI model integration with geopolitical analysis
// Load environment variables manually
const fs = require('fs');
const path = require('path');

// Try to load .env file
try {
  const envPath = path.join(__dirname, 'server', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  }
} catch (error) {
  console.log('Could not load .env file, using existing environment variables');
}

// Mock the logger
global.logger = {
  info: (...args) => console.log('[INFO]', ...args),
  warn: (...args) => console.warn('[WARN]', ...args),
  error: (...args) => console.error('[ERROR]', ...args)
};

const { NewsImpactAnalysisService } = require('./server/dist/services/NewsImpactAnalysisService');

const testArticle = {
  title: "Iran accuses US of 'waging war' under 'absurd pretext'",
  content: "Iran has accused the United States of waging war under an absurd pretext following recent military actions in the region. The Iranian government has threatened to retaliate against what it calls unprovoked aggression. Oil markets are watching closely as tensions escalate in the Middle East. Energy companies are monitoring the situation as oil prices surge on supply concerns.",
  source: "Reuters",
  publishedAt: new Date(),
  category: "world",
  url: "https://example.com/iran-conflict"
};

async function testAIIntegration() {
  console.log('🤖 Testing AI Model Integration with Geopolitical Analysis');
  console.log('=' .repeat(70));

  try {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      console.error('❌ GOOGLE_AI_API_KEY not found in environment variables');
      return;
    }

    console.log('✅ Google AI API Key found');
    console.log(`📰 Testing with article: ${testArticle.title}`);
    
    const newsImpactService = new NewsImpactAnalysisService(apiKey);
    
    console.log('\n🔍 Calling analyzeMarketImpact...');
    const analysis = await newsImpactService.analyzeMarketImpact(testArticle);
    
    console.log('\n📊 Analysis Results:');
    console.log('=' .repeat(50));
    console.log(`Impact Score: ${analysis.impactScore}`);
    console.log(`Impact Direction: ${analysis.impactDirection}`);
    console.log(`Confidence: ${analysis.confidence}`);
    console.log(`Timeframe: ${analysis.timeframe}`);
    console.log(`Affected Sectors: ${analysis.affectedSectors.length}`);
    console.log(`Affected Companies: ${analysis.affectedCompanies.length}`);
    console.log(`Trading Opportunities: ${analysis.tradingOpportunities.length}`);
    
    if (analysis.affectedSectors.length > 0) {
      console.log('\n🏭 Affected Sectors:');
      analysis.affectedSectors.forEach(sector => {
        console.log(`  • ${sector.sector}: Impact ${sector.impactScore}, Confidence ${sector.confidence}%`);
        console.log(`    Reasoning: ${sector.reasoning.substring(0, 150)}...`);
      });
    }
    
    if (analysis.affectedCompanies.length > 0) {
      console.log('\n🏢 Affected Companies:');
      analysis.affectedCompanies.forEach(company => {
        console.log(`  • ${company.ticker} (${company.companyName}): Impact ${company.impactScore}, Confidence ${company.confidence}%`);
        console.log(`    Reasoning: ${company.reasoning.substring(0, 150)}...`);
      });
    } else {
      console.log('\n❌ NO AFFECTED COMPANIES FOUND!');
      console.log('This indicates the AI model is not responding correctly to the geopolitical context.');
    }
    
    if (analysis.tradingOpportunities.length > 0) {
      console.log('\n💼 Trading Opportunities:');
      analysis.tradingOpportunities.forEach(opp => {
        console.log(`  • ${opp.type}: ${opp.tickers.join(', ')} - ${opp.reasoning.substring(0, 100)}...`);
      });
    }
    
    console.log('\n📝 Overall Reasoning:');
    console.log(analysis.reasoning.substring(0, 300) + '...');
    
    // Check if this is a fallback response
    if (analysis.confidence === 0 || analysis.impactScore === 0) {
      console.log('\n⚠️  WARNING: This appears to be a fallback response!');
      console.log('The AI model may not be responding correctly or JSON parsing failed.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testAIIntegration();
