import { GoogleGenerativeAI } from '@google/generative-ai';
import { SentimentAnalysis, NewsArticle } from '../types';
import { ENHANCED_SENTIMENT_PROMPT } from '../config/settings';
import { logger } from '../utils/logger';
import axios from 'axios';

export interface EnhancedSentimentAnalysis extends SentimentAnalysis {
  mediumTermImpact: boolean;
  longTermImpact: boolean;
  fundamentalImpact: string;
  technicalImplications: string;
  riskFactors: string[];
  catalysts: string[];
  sectorImplications: string;
  institutionalView: string;
  priceTargetImpact: string;
  volumeExpectation: string;
  optionsActivity: string;
  marketCapImpact: string;
  sourceCredibility: number; // 1-10 scale
  sentimentScore: number; // -1 to 1 numerical score
}

export interface MarketContext {
  marketCap: string;
  sector: string;
  priceAction: string;
  currentPrice: number;
  volume: number;
  marketRegime: 'bull' | 'bear' | 'sideways';
  sectorPerformance: number;
  vixLevel: number;
}

export class EnhancedSentimentAnalysisService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private sourceCredibilityMap: Map<string, number>;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemma-3-27b-it' });
    
    // Initialize source credibility mapping
    this.sourceCredibilityMap = new Map([
      ['reuters.com', 9],
      ['bloomberg.com', 9],
      ['wsj.com', 9],
      ['ft.com', 9],
      ['cnbc.com', 8],
      ['marketwatch.com', 8],
      ['yahoo.com', 7],
      ['seekingalpha.com', 7],
      ['fool.com', 6],
      ['benzinga.com', 6],
      ['default', 5]
    ]);
  }

  /**
   * Get source credibility score
   */
  private getSourceCredibility(source: string): number {
    const domain = source.toLowerCase();
    for (const [key, value] of this.sourceCredibilityMap.entries()) {
      if (domain.includes(key)) {
        return value;
      }
    }
    return this.sourceCredibilityMap.get('default') || 5;
  }

  /**
   * Fetch market context for enhanced analysis
   */
  private async getMarketContext(ticker: string): Promise<MarketContext> {
    try {
      // This would typically fetch from a financial data API
      // For now, returning mock data - in production, integrate with Yahoo Finance, Alpha Vantage, etc.
      return {
        marketCap: 'Large Cap',
        sector: 'Technology',
        priceAction: 'Consolidating near resistance',
        currentPrice: 150.00,
        volume: 1000000,
        marketRegime: 'bull',
        sectorPerformance: 0.05,
        vixLevel: 18.5
      };
    } catch (error) {
      logger.error('Error fetching market context:', error);
      return {
        marketCap: 'Unknown',
        sector: 'Unknown',
        priceAction: 'Unknown',
        currentPrice: 0,
        volume: 0,
        marketRegime: 'sideways',
        sectorPerformance: 0,
        vixLevel: 20
      };
    }
  }

  /**
   * Enhanced sentiment analysis with financial market context
   */
  public async analyzeEnhancedSentiment(
    article: NewsArticle, 
    ticker: string
  ): Promise<EnhancedSentimentAnalysis> {
    try {
      const marketContext = await this.getMarketContext(ticker);
      const sourceCredibility = this.getSourceCredibility(article.source);

      const prompt = ENHANCED_SENTIMENT_PROMPT
        .replace('{ticker}', ticker)
        .replace('{marketCap}', marketContext.marketCap)
        .replace('{sector}', marketContext.sector)
        .replace('{priceAction}', marketContext.priceAction)
        .replace('{title}', article.title)
        .replace('{content}', article.content.substring(0, 2000))
        .replace('{source}', article.source)
        .replace('{publishedAt}', new Date(article.publishedAt).toISOString());

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const sentimentData = this.parseEnhancedSentimentResponse(text, sourceCredibility);
      
      logger.info('Enhanced sentiment analysis completed', { 
        ticker, 
        sentiment: sentimentData.sentiment,
        confidence: sentimentData.confidence,
        sourceCredibility,
        sentimentScore: sentimentData.sentimentScore
      });

      return sentimentData;
    } catch (error) {
      logger.error('Error in enhanced sentiment analysis:', error);
      
      return this.getFallbackSentiment();
    }
  }

  /**
   * Parse enhanced AI response
   */
  private parseEnhancedSentimentResponse(
    text: string, 
    sourceCredibility: number
  ): EnhancedSentimentAnalysis {
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const data = JSON.parse(jsonMatch[0]);
        
        return {
          sentiment: data.sentiment || 'Neutral',
          confidence: data.confidence || 'Low',
          summary: data.summary || 'No summary available',
          shortTermImpact: data.shortTermImpact || false,
          mediumTermImpact: data.mediumTermImpact || false,
          longTermImpact: data.longTermImpact || false,
          reasoning: data.reasoning || 'No reasoning provided',
          fundamentalImpact: data.fundamentalImpact || 'Unknown',
          technicalImplications: data.technicalImplications || 'Unknown',
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || [],
          sectorImplications: data.sectorImplications || 'Unknown',
          institutionalView: data.institutionalView || 'Unknown',
          priceTargetImpact: data.priceTargetImpact || 'Unknown',
          volumeExpectation: data.volumeExpectation || 'Unknown',
          optionsActivity: data.optionsActivity || 'Unknown',
          marketCapImpact: data.marketCapImpact || 'Unknown',
          sourceCredibility,
          sentimentScore: this.calculateSentimentScore(data.sentiment, data.confidence, sourceCredibility)
        };
      }

      return this.getFallbackSentiment(sourceCredibility);
    } catch (error) {
      logger.error('Error parsing enhanced sentiment response:', error);
      return this.getFallbackSentiment(sourceCredibility);
    }
  }

  /**
   * Calculate numerical sentiment score
   */
  private calculateSentimentScore(
    sentiment: string, 
    confidence: string, 
    sourceCredibility: number
  ): number {
    let baseScore = 0;
    switch (sentiment) {
      case 'Positive': baseScore = 1; break;
      case 'Negative': baseScore = -1; break;
      default: baseScore = 0;
    }

    let confidenceMultiplier = 0.5;
    switch (confidence) {
      case 'High': confidenceMultiplier = 1.0; break;
      case 'Medium': confidenceMultiplier = 0.7; break;
      default: confidenceMultiplier = 0.4;
    }

    const credibilityMultiplier = sourceCredibility / 10;
    
    return baseScore * confidenceMultiplier * credibilityMultiplier;
  }

  /**
   * Fallback sentiment for errors
   */
  private getFallbackSentiment(sourceCredibility: number = 5): EnhancedSentimentAnalysis {
    return {
      sentiment: 'Neutral',
      confidence: 'Low',
      summary: 'Unable to analyze sentiment due to technical error',
      shortTermImpact: false,
      mediumTermImpact: false,
      longTermImpact: false,
      reasoning: 'Analysis failed, defaulting to neutral',
      fundamentalImpact: 'Unknown',
      technicalImplications: 'Unknown',
      riskFactors: [],
      catalysts: [],
      sectorImplications: 'Unknown',
      institutionalView: 'Unknown',
      priceTargetImpact: 'Unknown',
      volumeExpectation: 'Unknown',
      optionsActivity: 'Unknown',
      marketCapImpact: 'Unknown',
      sourceCredibility,
      sentimentScore: 0
    };
  }

  /**
   * Aggregate sentiment from multiple sources with weighting
   */
  public async analyzeMultiSourceSentiment(
    articles: NewsArticle[], 
    ticker: string
  ): Promise<{
    overallSentiment: 'Positive' | 'Neutral' | 'Negative';
    averageConfidence: 'High' | 'Medium' | 'Low';
    weightedSentimentScore: number;
    summary: string;
    articlesWithSentiment: (NewsArticle & { sentiment: EnhancedSentimentAnalysis })[];
    riskFactors: string[];
    catalysts: string[];
    consensusView: string;
  }> {
    const articlesWithSentiment: (NewsArticle & { sentiment: EnhancedSentimentAnalysis })[] = [];
    
    // Analyze each article
    for (const article of articles) {
      const sentiment = await this.analyzeEnhancedSentiment(article, ticker);
      articlesWithSentiment.push({ ...article, sentiment });
    }

    // Calculate weighted sentiment score
    let totalWeightedScore = 0;
    let totalWeight = 0;
    const allRiskFactors = new Set<string>();
    const allCatalysts = new Set<string>();

    articlesWithSentiment.forEach(article => {
      const weight = article.sentiment.sourceCredibility;
      totalWeightedScore += article.sentiment.sentimentScore * weight;
      totalWeight += weight;
      
      article.sentiment.riskFactors.forEach(risk => allRiskFactors.add(risk));
      article.sentiment.catalysts.forEach(catalyst => allCatalysts.add(catalyst));
    });

    const weightedSentimentScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

    // Determine overall sentiment
    let overallSentiment: 'Positive' | 'Neutral' | 'Negative' = 'Neutral';
    if (weightedSentimentScore > 0.2) {
      overallSentiment = 'Positive';
    } else if (weightedSentimentScore < -0.2) {
      overallSentiment = 'Negative';
    }

    // Calculate average confidence
    const avgConfidenceScore = articlesWithSentiment.reduce((sum, article) => {
      const score = article.sentiment.confidence === 'High' ? 3 : 
                   article.sentiment.confidence === 'Medium' ? 2 : 1;
      return sum + score;
    }, 0) / articlesWithSentiment.length;

    const averageConfidence: 'High' | 'Medium' | 'Low' = 
      avgConfidenceScore >= 2.5 ? 'High' : avgConfidenceScore >= 1.5 ? 'Medium' : 'Low';

    const summary = `Analyzed ${articlesWithSentiment.length} articles with weighted sentiment score of ${weightedSentimentScore.toFixed(3)}. Overall sentiment: ${overallSentiment} with ${averageConfidence} confidence.`;

    const consensusView = this.generateConsensusView(articlesWithSentiment, overallSentiment);

    return {
      overallSentiment,
      averageConfidence,
      weightedSentimentScore,
      summary,
      articlesWithSentiment,
      riskFactors: Array.from(allRiskFactors),
      catalysts: Array.from(allCatalysts),
      consensusView
    };
  }

  /**
   * Generate consensus view from multiple analyses
   */
  private generateConsensusView(
    articlesWithSentiment: (NewsArticle & { sentiment: EnhancedSentimentAnalysis })[], 
    overallSentiment: string
  ): string {
    const institutionalViews = articlesWithSentiment.map(a => a.sentiment.institutionalView);
    const fundamentalImpacts = articlesWithSentiment.map(a => a.sentiment.fundamentalImpact);
    
    return `Market consensus appears ${overallSentiment.toLowerCase()} based on ${articlesWithSentiment.length} sources. Institutional sentiment suggests cautious optimism with focus on fundamental developments.`;
  }
}
