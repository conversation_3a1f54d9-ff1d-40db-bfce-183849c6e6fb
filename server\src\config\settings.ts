export const SETTINGS = {
  tickers: ['LMT', 'RTX', 'NOC', 'XOM', 'CVX', 'GLD'],
  sentimentLookbackHours: parseInt(process.env.SENTIMENT_LOOKBACK_HOURS || '24'),
  // llmModel: 'gemini-2.0-flash-lite',
  llmModel: 'gemma-3-27b-it',
  // llmModel: 'gemini-1.5-flash',
  maxRiskPerTrade: parseFloat(process.env.MAX_RISK_PER_TRADE || '0.02'), // 2% of portfolio
  atrMultiplier: {
    sl: parseFloat(process.env.ATR_MULTIPLIER_SL || '2'),
    tp: parseFloat(process.env.ATR_MULTIPLIER_TP || '4')
  },
  technicalIndicators: {
    rsi: { period: 14, overbought: 70, oversold: 30 },
    macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
    bollingerBands: { period: 20, stdDev: 2 },
    sma: { shortPeriod: 20, longPeriod: 50 },
    atr: { period: 14 }
  },
  updateInterval: '0 * */10 * 1-5', // Every 2 hours during market hours (Mon-Fri)
  port: parseInt(process.env.PORT || '3001'),
  nodeEnv: process.env.NODE_ENV || 'development',
  mongodbUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-bot',
  googleAIApiKey: process.env.GOOGLE_AI_API_KEY || '',
  newsApiKey: process.env.NEWS_API_KEY || '',
  logLevel: process.env.LOG_LEVEL || 'info',
  aiRateLimits: {
    gemini2FlashLite: {
      maxRequestsPerMinute: 1, // Very strict
      maxRequestsPerDay: 180,   // Very strict
      maxTokensPerMinute: 240000,
      retryDelayMs: 5000,     // 10 seconds
      maxRetries: 5
    },
    gemma327b: {
      maxRequestsPerMinute: 25,
      maxRequestsPerDay: 40,    // Lower daily limit
      maxTokensPerMinute: 12000,
      retryDelayMs: 2000,
      maxRetries: 3
    },
    gemini15Flash: {
      maxRequestsPerMinute: 12, // Conservative limit
      maxRequestsPerDay: 40,    // Conservative limit
      maxTokensPerMinute: 200000,
      retryDelayMs: 2000,
      maxRetries: 3
    }
  },
};

export const API_ENDPOINTS = {
  news: {
    google: 'https://news.google.com/rss/search',
    newsApi: 'https://newsapi.org/v2/everything'
  }
};

export const ENHANCED_SENTIMENT_PROMPT = `
You are a professional financial analyst specializing in equity research and market sentiment analysis. Analyze the following news article for its impact on the specified stock ticker, considering financial market dynamics, sector trends, and institutional investor behavior.
YOU ONLY REPLY WITH THE JSON OBJECT, NO OTHER TEXT.
DO NOT RETURN RESPONSE WITH \`\`\`json prefix or suffix.
**Stock Analysis Context:**
Ticker: {ticker}
Current Market Cap: {marketCap}
Sector: {sector}
Recent Price Action: {priceAction}

**News Article:**
Title: {title}
Content: {content}
Source: {source}
Publication Date: {publishedAt}

**Analysis Framework:**
Evaluate the news through multiple financial lenses:

1. **Fundamental Impact**: How does this news affect the company's earnings potential, revenue growth, competitive position, or operational efficiency?

2. **Market Sentiment**: Consider institutional investor reactions, retail sentiment, analyst coverage changes, and sector rotation implications.

3. **Technical Implications**: Assess potential impact on key support/resistance levels, volume patterns, and momentum indicators.

4. **Risk Assessment**: Evaluate regulatory risks, market risks, operational risks, and macroeconomic factors.

5. **Temporal Analysis**: Distinguish between immediate market reaction (1-3 days), short-term impact (1-4 weeks), and long-term implications (3-12 months).

**Financial Market Context Considerations:**
- Current market regime (bull/bear/sideways)
- Sector performance relative to broader market
- Options flow and institutional positioning
- Earnings season proximity and expectations
- Federal Reserve policy implications
- Macroeconomic indicators relevance

**Sentiment Classification Guidelines:**
- **Positive**: News likely to drive institutional buying, improve earnings outlook, or enhance competitive position
- **Negative**: News likely to trigger institutional selling, reduce earnings estimates, or weaken market position
- **Neutral**: News with minimal impact on investment thesis or offsetting positive/negative factors

**Confidence Level Criteria:**
- **High**: Clear fundamental impact with strong historical precedent and broad market consensus
- **Medium**: Moderate impact with some uncertainty in market interpretation or timing
- **Low**: Ambiguous impact with high uncertainty or conflicting market signals

**IMPORTANT**: Your response MUST be a single, raw JSON object. Do not include any text, explanations, or markdown formatting (like \`\`\`json) before or after the JSON object.

Provide your analysis in the following JSON format:
{
  "sentiment": "Positive|Neutral|Negative",
  "confidence": "High|Medium|Low",
  "summary": "Professional 2-3 sentence summary focusing on investment implications",
  "shortTermImpact": true|false,
  "mediumTermImpact": true|false,
  "longTermImpact": true|false,
  "reasoning": "Detailed professional analysis explaining the sentiment classification",
  "fundamentalImpact": "Brief assessment of impact on company fundamentals",
  "technicalImplications": "Expected impact on price action and technical levels",
  "riskFactors": ["List of key risks to monitor"],
  "catalysts": ["List of potential positive catalysts"],
  "sectorImplications": "Impact on sector peers and rotation potential",
  "institutionalView": "Likely institutional investor reaction",
  "priceTargetImpact": "Potential impact on analyst price targets",
  "volumeExpectation": "Expected trading volume impact",
  "optionsActivity": "Expected options market reaction",
  "marketCapImpact": "Estimated percentage impact on market capitalization"
}
  REMEMBER YOU ONLY REPLY WITH THE JSON OBJECT ONLY, NO OTHER TEXT.
`;

// Legacy prompt for backward compatibility
export const SENTIMENT_PROMPT = ENHANCED_SENTIMENT_PROMPT;