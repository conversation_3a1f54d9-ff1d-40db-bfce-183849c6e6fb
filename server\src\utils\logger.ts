import winston from 'winston';
import path from 'path';
import fs from 'fs';

const logLevel = process.env.LOG_LEVEL || 'info';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// New, cleaner log format
const cleanFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, component, operation, duration, ...meta }) => {
    let logMessage = `[${timestamp}] [${level.toUpperCase()}] [${component || 'General'}]`;

    if (operation) {
      logMessage += ` - ${operation}`;
    }

    logMessage += `: ${message}`;

    if (duration !== undefined) {
      logMessage += ` (${duration}ms)`;
    }

    // Format meta objects for better readability
    if (Object.keys(meta).length > 0) {
      const formattedMeta = Object.entries(meta).map(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          return `${key}=${JSON.stringify(value)}`;
        }
        return `${key}=${value}`;
      }).join(', ');
      logMessage += ` | {${formattedMeta}}`;
    }

    return logMessage;
  })
);

export const logger = winston.createLogger({
  level: logLevel,
  format: cleanFormat,
  defaultMeta: { service: 'trading-bot' },
  transports: [
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
    }),
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
    }),
    new winston.transports.File({
      filename: path.join(logsDir, 'api.log'),
      level: 'info',
    }),
    new winston.transports.File({
      filename: path.join(logsDir, 'data-processing.log'),
      level: 'info',
    }),
  ],
});

// If we're not in production, log to the console as well
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      cleanFormat
    )
  }));
}

// Standardized Logging Helper Functions

/**
 * Logs an API request.
 */
export const logAPIRequest = (method: string, url: string, statusCode: number, duration: number, meta?: object) => {
  const message = `${method} ${url} - Status ${statusCode}`;
  logger.info(message, { component: 'API', duration, ...meta });
};

/**
 * Logs a successful discovery event.
 */
export const logDiscovery = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'Discovery', operation, ...meta });
};

/**
 * Logs a data processing operation.
 */
export const logData = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'Data', operation, ...meta });
};

/**
 * Logs a generic error.
 */
export const logError = (component: string, operation: string, error: Error, meta?: object) => {
  logger.error(`${component} - ${operation}`, {
    error: {
      message: error.message,
      stack: error.stack,
    },
    ...meta,
  });
};

/**
 * Logs a performance metric.
 */
export const logPerformance = (component: string, operation: string, duration: number, meta?: object) => {
  const level = duration > 5000 ? 'warn' : 'info';
  logger.log(level, `Operation took ${duration}ms`, {
    component,
    operation,
    duration,
    ...meta,
  });
};

/**
 * Logs a generic warning.
 */
export const logWarn = (component: string, operation: string, message: string, meta?: object) => {
  logger.warn(message, { component, operation, ...meta });
};

/**
 * Logs geopolitical analysis events with enhanced formatting.
 */
export const logGeopolitical = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'Geopolitical', operation, ...meta });
};

/**
 * Logs AI model interactions with detailed context.
 */
export const logAI = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'AI-Model', operation, ...meta });
};

/**
 * Logs ticker discovery events with enhanced details.
 */
export const logTickerDiscovery = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'TickerDiscovery', operation, ...meta });
};

/**
 * Logs analysis pipeline events for debugging.
 */
export const logAnalysisPipeline = (operation: string, message: string, meta?: object) => {
  logger.info(message, { component: 'AnalysisPipeline', operation, ...meta });
};