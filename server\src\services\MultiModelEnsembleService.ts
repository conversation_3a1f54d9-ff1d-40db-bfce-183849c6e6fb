import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '../utils/logger';
import { GlobalNewsArticle } from './GlobalNewsService';
import { CompanyProfile } from './CompanyProfileService';
import { MarketImpactAnalysis, CompanyImpact } from './NewsImpactAnalysisService';
import { SETTINGS } from '../config/settings';

export interface ModelResult {
  modelName: string;
  analysis: MarketImpactAnalysis;
  processingTime: number;
  confidence: number;
  success: boolean;
  error?: string;
}

export interface EnsembleResult {
  primaryAnalysis: MarketImpactAnalysis;
  modelResults: ModelResult[];
  consensusScore: number; // 0-100
  agreementLevel: 'high' | 'medium' | 'low';
  validatedTickers: string[];
  rejectedTickers: { ticker: string; reason: string }[];
  overallConfidence: number;
  processingTime: number;
}

export interface TickerConsensus {
  ticker: string;
  votes: number;
  totalModels: number;
  averageImpactScore: number;
  averageConfidence: number;
  consensusStrength: number; // 0-1
  agreementReasons: string[];
  disagreementReasons: string[];
}

export class MultiModelEnsembleService {
  private static instance: MultiModelEnsembleService;
  private models: Map<string, any> = new Map();
  private modelWeights: Map<string, number> = new Map();

  constructor(private geminiApiKey: string) {
    this.initializeModels();
  }

  public static getInstance(geminiApiKey: string): MultiModelEnsembleService {
    if (!MultiModelEnsembleService.instance) {
      MultiModelEnsembleService.instance = new MultiModelEnsembleService(geminiApiKey);
    }
    return MultiModelEnsembleService.instance;
  }

  /**
   * Initialize multiple AI models for ensemble analysis
   */
  private initializeModels(): void {
    try {
      const genAI = new GoogleGenerativeAI(this.geminiApiKey);

      // Primary model - Gemini Flash (fast, good for validation)
      this.models.set(SETTINGS.llmModel, genAI.getGenerativeModel({ model: SETTINGS.llmModel }));
      this.modelWeights.set(SETTINGS.llmModel, 1);

      // // Secondary model - Gemini Pro (good balance)
      // this.models.set('gemini-1.5-pro', genAI.getGenerativeModel({ model: 'gemini-1.5-pro' }));
      // this.modelWeights.set('gemini-1.5-pro', 0.3);

      // // Tertiary model - Gemini Flash (fast, good for validation)
      // this.models.set('gemini-1.5-flash', genAI.getGenerativeModel({ model: 'gemini-1.5-flash' }));
      // this.modelWeights.set('gemini-1.5-flash', 0.3);

      logger.info('Multi-model ensemble initialized with 1 model');
    } catch (error) {
      logger.error('Error initializing ensemble models:', error);
      throw error;
    }
  }

  /**
   * Analyze news with multiple models and create ensemble result
   */
  public async analyzeWithEnsemble(
    article: GlobalNewsArticle,
    companyProfiles?: CompanyProfile[]
  ): Promise<EnsembleResult> {
    const startTime = Date.now();
    const modelResults: ModelResult[] = [];

    try {
      // Run analysis with all models in parallel
      const analysisPromises = Array.from(this.models.entries()).map(async ([modelName, model]) => {
        return this.runModelAnalysis(modelName, model, article, companyProfiles);
      });

      const results = await Promise.allSettled(analysisPromises);
      
      // Process results
      results.forEach((result, index) => {
        const modelName = Array.from(this.models.keys())[index];
        if (result.status === 'fulfilled') {
          modelResults.push(result.value);
        } else {
          modelResults.push({
            modelName,
            analysis: this.getFallbackAnalysis(),
            processingTime: 0,
            confidence: 0,
            success: false,
            error: result.reason?.message || 'Unknown error'
          });
        }
      });

      // Create ensemble analysis
      const ensembleAnalysis = this.createEnsembleAnalysis(modelResults);
      
      // Calculate consensus metrics
      const consensusMetrics = this.calculateConsensusMetrics(modelResults);
      
      // Validate tickers through ensemble voting
      const tickerValidation = this.validateTickersWithEnsemble(modelResults);

      const processingTime = Date.now() - startTime;

      return {
        primaryAnalysis: ensembleAnalysis,
        modelResults,
        consensusScore: consensusMetrics.consensusScore,
        agreementLevel: consensusMetrics.agreementLevel,
        validatedTickers: tickerValidation.validatedTickers,
        rejectedTickers: tickerValidation.rejectedTickers,
        overallConfidence: consensusMetrics.overallConfidence,
        processingTime
      };
    } catch (error) {
      logger.error('Error in ensemble analysis:', error);
      throw error;
    }
  }

  /**
   * Run analysis with a single model
   */
  private async runModelAnalysis(
    modelName: string,
    model: any,
    article: GlobalNewsArticle,
    companyProfiles?: CompanyProfile[]
  ): Promise<ModelResult> {
    const startTime = Date.now();

    try {
      const prompt = this.buildModelSpecificPrompt(modelName, article, companyProfiles);
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const analysis = this.parseModelResponse(text, modelName);
      const processingTime = Date.now() - startTime;

      return {
        modelName,
        analysis,
        processingTime,
        confidence: analysis.confidence,
        success: true
      };
    } catch (error) {
      logger.error(`Error in ${modelName} analysis:`, error);
      return {
        modelName,
        analysis: this.getFallbackAnalysis(),
        processingTime: Date.now() - startTime,
        confidence: 0,
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Build model-specific prompts
   */
  private buildModelSpecificPrompt(
    modelName: string,
    article: GlobalNewsArticle,
    companyProfiles?: CompanyProfile[]
  ): string {
    const basePrompt = this.getBasePrompt(article, companyProfiles);
    
    // Model-specific adjustments
    switch (modelName) {
      case 'gemma-3-27b-it':
        return `${basePrompt}\n\n**ROLE: PRIMARY ANALYST** - Provide comprehensive analysis with detailed reasoning.`;
      
      case 'gemini-pro':
        return `${basePrompt}\n\n**ROLE: VALIDATION ANALYST** - Focus on validating causal relationships and business relevance.`;
      
      case 'gemini-1.5-flash':
        return `${basePrompt}\n\n**ROLE: RAPID VALIDATOR** - Quickly identify obvious false positives and validate ticker relevance.`;
      
      default:
        return basePrompt;
    }
  }

  /**
   * Get base analysis prompt
   */
  private getBasePrompt(article: GlobalNewsArticle, companyProfiles?: CompanyProfile[]): string {
    const companyContext = companyProfiles ? this.buildCompanyContext(companyProfiles) : '';
    
    return `
Analyze this news article for market impact with strict validation requirements:

**NEWS ARTICLE:**
Title: ${article.title}
Content: ${article.content}
Source: ${article.source}

${companyContext}

**VALIDATION REQUIREMENTS:**
1. Only include tickers with clear causal chains: News → Business → Financial → Market
2. Avoid word-similarity false positives (e.g., "navigation" ≠ NAVI unless about student loans)
3. Validate business model relevance
4. Provide confidence scores for each causal link

**RESPONSE FORMAT:** JSON only with fields: impactScore, impactDirection, timeframe, confidence, affectedSectors, affectedCompanies, reasoning, tradingOpportunities, riskFactors, catalysts.`;
  }

  /**
   * Build company context
   */
  private buildCompanyContext(companyProfiles: CompanyProfile[]): string {
    if (companyProfiles.length === 0) return '';

    const contextLines = companyProfiles.map(profile => 
      `${profile.ticker}: ${profile.businessModel} (${profile.sector})`
    );

    return `\n**COMPANY CONTEXT:**\n${contextLines.join('\n')}`;
  }

  /**
   * Parse model response
   */
  private parseModelResponse(text: string, modelName: string): MarketImpactAnalysis {
    try {
      const jsonMatch = text.match(/```json([\s\S]*?)```/) || text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonString = jsonMatch[1] || jsonMatch[0];
        const data = JSON.parse(jsonString.trim());
        
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || `Analysis from ${modelName}`,
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }
    } catch (error) {
      logger.error(`Error parsing ${modelName} response:`, error);
    }

    return this.getFallbackAnalysis();
  }

  /**
   * Create ensemble analysis from multiple model results
   */
  private createEnsembleAnalysis(modelResults: ModelResult[]): MarketImpactAnalysis {
    const successfulResults = modelResults.filter(result => result.success);
    
    if (successfulResults.length === 0) {
      return this.getFallbackAnalysis();
    }

    // Weighted averaging based on model weights and confidence
    let totalWeight = 0;
    let weightedImpactScore = 0;
    let weightedConfidence = 0;
    
    const allSectors = new Map<string, { score: number; weight: number; reasoning: string[] }>();
    const allCompanies = new Map<string, { impact: CompanyImpact; weight: number }>();
    const allOpportunities: any[] = [];
    const allRiskFactors = new Set<string>();
    const allCatalysts = new Set<string>();
    const reasoningParts: string[] = [];

    successfulResults.forEach(result => {
      const weight = this.modelWeights.get(result.modelName) || 0.1;
      const confidenceWeight = weight * (result.confidence / 100);
      
      totalWeight += confidenceWeight;
      weightedImpactScore += result.analysis.impactScore * confidenceWeight;
      weightedConfidence += result.analysis.confidence * confidenceWeight;

      // Aggregate sectors
      result.analysis.affectedSectors.forEach(sector => {
        const key = sector.sector;
        if (!allSectors.has(key)) {
          allSectors.set(key, { score: 0, weight: 0, reasoning: [] });
        }
        const existing = allSectors.get(key)!;
        existing.score += sector.impactScore * confidenceWeight;
        existing.weight += confidenceWeight;
        existing.reasoning.push(sector.reasoning);
      });

      // Aggregate companies
      result.analysis.affectedCompanies.forEach(company => {
        const key = company.ticker || company.companyName;
        if (!allCompanies.has(key)) {
          allCompanies.set(key, { impact: company, weight: 0 });
        }
        const existing = allCompanies.get(key)!;
        existing.impact.impactScore = (existing.impact.impactScore * existing.weight + company.impactScore * confidenceWeight) / (existing.weight + confidenceWeight);
        existing.impact.confidence = (existing.impact.confidence * existing.weight + company.confidence * confidenceWeight) / (existing.weight + confidenceWeight);
        existing.weight += confidenceWeight;
      });

      // Collect other fields
      allOpportunities.push(...result.analysis.tradingOpportunities);
      result.analysis.riskFactors.forEach(risk => allRiskFactors.add(risk));
      result.analysis.catalysts.forEach(catalyst => allCatalysts.add(catalyst));
      reasoningParts.push(`${result.modelName}: ${result.analysis.reasoning}`);
    });

    // Normalize weighted values
    const finalImpactScore = totalWeight > 0 ? weightedImpactScore / totalWeight : 0;
    const finalConfidence = totalWeight > 0 ? weightedConfidence / totalWeight : 0;

    // Process aggregated sectors
    const finalSectors = Array.from(allSectors.entries()).map(([sector, data]) => ({
      sector,
      impactScore: data.weight > 0 ? data.score / data.weight : 0,
      reasoning: data.reasoning.join('; '),
      confidence: finalConfidence,
      timeframe: 'short_term'
    }));

    // Process aggregated companies
    const finalCompanies = Array.from(allCompanies.values()).map(data => data.impact);

    // Determine overall direction and timeframe
    const impactDirection = finalImpactScore > 10 ? 'positive' : finalImpactScore < -10 ? 'negative' : 'neutral';
    const timeframe = this.determineEnsembleTimeframe(successfulResults) as 'immediate' | 'short_term' | 'medium_term' | 'long_term';

    return {
      impactScore: Math.round(finalImpactScore),
      impactDirection,
      timeframe,
      confidence: Math.round(finalConfidence),
      affectedSectors: finalSectors,
      affectedCompanies: finalCompanies,
      reasoning: `Ensemble analysis: ${reasoningParts.join(' | ')}`,
      tradingOpportunities: this.deduplicateOpportunities(allOpportunities),
      riskFactors: Array.from(allRiskFactors),
      catalysts: Array.from(allCatalysts)
    };
  }

  /**
   * Calculate consensus metrics
   */
  private calculateConsensusMetrics(modelResults: ModelResult[]): {
    consensusScore: number;
    agreementLevel: 'high' | 'medium' | 'low';
    overallConfidence: number;
  } {
    const successfulResults = modelResults.filter(result => result.success);

    // Handle single model case (no ensemble)
    if (successfulResults.length === 1) {
      const singleResult = successfulResults[0];
      return {
        consensusScore: singleResult.analysis.confidence,
        agreementLevel: singleResult.analysis.confidence > 70 ? 'high' : 'medium',
        overallConfidence: singleResult.analysis.confidence
      };
    }

    if (successfulResults.length < 1) {
      return { consensusScore: 0, agreementLevel: 'low', overallConfidence: 0 };
    }

    // Calculate impact score variance
    const impactScores = successfulResults.map(result => result.analysis.impactScore);
    const avgImpactScore = impactScores.reduce((sum, score) => sum + score, 0) / impactScores.length;
    const variance = impactScores.reduce((sum, score) => sum + Math.pow(score - avgImpactScore, 2), 0) / impactScores.length;
    const standardDeviation = Math.sqrt(variance);

    // Calculate consensus score (lower variance = higher consensus)
    const consensusScore = Math.max(0, 100 - (standardDeviation * 2));

    // Determine agreement level
    let agreementLevel: 'high' | 'medium' | 'low';
    if (consensusScore > 80) agreementLevel = 'high';
    else if (consensusScore > 60) agreementLevel = 'medium';
    else agreementLevel = 'low';

    // Calculate overall confidence
    const confidences = successfulResults.map(result => result.analysis.confidence);
    const overallConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;

    return { consensusScore, agreementLevel, overallConfidence };
  }

  /**
   * Validate tickers through ensemble voting
   */
  private validateTickersWithEnsemble(modelResults: ModelResult[]): {
    validatedTickers: string[];
    rejectedTickers: { ticker: string; reason: string }[];
  } {
    const tickerVotes = new Map<string, TickerConsensus>();
    const successfulResults = modelResults.filter(result => result.success);

    // Collect votes for each ticker
    successfulResults.forEach(result => {
      result.analysis.affectedCompanies.forEach(company => {
        if (company.ticker) {
          if (!tickerVotes.has(company.ticker)) {
            tickerVotes.set(company.ticker, {
              ticker: company.ticker,
              votes: 0,
              totalModels: successfulResults.length,
              averageImpactScore: 0,
              averageConfidence: 0,
              consensusStrength: 0,
              agreementReasons: [],
              disagreementReasons: []
            });
          }

          const consensus = tickerVotes.get(company.ticker)!;
          consensus.votes++;
          consensus.averageImpactScore += company.impactScore;
          consensus.averageConfidence += company.confidence;
          consensus.agreementReasons.push(company.reasoning);
        }
      });
    });

    // Calculate final consensus metrics
    tickerVotes.forEach(consensus => {
      consensus.averageImpactScore /= consensus.votes;
      consensus.averageConfidence /= consensus.votes;
      consensus.consensusStrength = consensus.votes / consensus.totalModels;
    });

    // Validate tickers based on consensus
    const validatedTickers: string[] = [];
    const rejectedTickers: { ticker: string; reason: string }[] = [];

    tickerVotes.forEach(consensus => {
      // For single model, just check confidence threshold
      if (successfulResults.length === 1) {
        if (consensus.averageConfidence > 50) { // Lower threshold for single model
          validatedTickers.push(consensus.ticker);
        } else {
          rejectedTickers.push({
            ticker: consensus.ticker,
            reason: `Low confidence (${Math.round(consensus.averageConfidence)}%)`
          });
        }
      } else {
        // Multi-model ensemble logic
        const minVotes = Math.ceil(successfulResults.length * 0.6); // 60% agreement required

        if (consensus.votes >= minVotes && consensus.averageConfidence > 60) {
          validatedTickers.push(consensus.ticker);
        } else {
          const reason = consensus.votes < minVotes ?
            `Insufficient model agreement (${consensus.votes}/${successfulResults.length})` :
            `Low average confidence (${Math.round(consensus.averageConfidence)}%)`;
          rejectedTickers.push({ ticker: consensus.ticker, reason });
        }
      }
    });

    return { validatedTickers, rejectedTickers };
  }

  /**
   * Determine ensemble timeframe
   */
  private determineEnsembleTimeframe(modelResults: ModelResult[]): string {
    const timeframes = modelResults.map(result => result.analysis.timeframe);
    const timeframeCounts: { [key: string]: number } = {};
    
    timeframes.forEach(tf => {
      timeframeCounts[tf] = (timeframeCounts[tf] || 0) + 1;
    });

    return Object.keys(timeframeCounts).reduce((a, b) => 
      timeframeCounts[a] > timeframeCounts[b] ? a : b
    );
  }

  /**
   * Deduplicate trading opportunities
   */
  private deduplicateOpportunities(opportunities: any[]): any[] {
    const uniqueOpportunities = new Map<string, any>();
    
    opportunities.forEach(opp => {
      const key = `${opp.type}-${opp.tickers?.sort().join(',') || ''}`;
      if (!uniqueOpportunities.has(key) || uniqueOpportunities.get(key).confidence < opp.confidence) {
        uniqueOpportunities.set(key, opp);
      }
    });

    return Array.from(uniqueOpportunities.values());
  }

  /**
   * Get fallback analysis
   */
  private getFallbackAnalysis(): MarketImpactAnalysis {
    return {
      impactScore: 0,
      impactDirection: 'neutral',
      timeframe: 'short_term',
      confidence: 0,
      affectedSectors: [],
      affectedCompanies: [],
      reasoning: 'Ensemble analysis failed',
      tradingOpportunities: [],
      riskFactors: ['Analysis uncertainty'],
      catalysts: []
    };
  }
}
