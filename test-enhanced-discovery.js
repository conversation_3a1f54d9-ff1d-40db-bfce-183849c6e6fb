// Test the enhanced discovery pipeline
const axios = require('axios');

async function testEnhancedDiscovery() {
  console.log('🚀 Testing Enhanced Discovery Pipeline');
  console.log('=' .repeat(60));

  try {
    // Test with the geopolitical articles we know are available
    const testArticles = [
      {
        title: "Iran accuses US of 'waging war' under 'absurd pretext'",
        content: "Iran's representative to the UN Security Council has accused the US of having waged a war against Iran under an absurd pretext following recent military actions in the region. The Iranian government has threatened to retaliate against what it calls unprovoked aggression. Oil markets are watching closely as tensions escalate in the Middle East.",
        source: "BBC News",
        publishedAt: new Date().toISOString(),
        category: "general",
        url: "https://example.com/iran-conflict"
      },
      {
        title: "U.S. calls on China to prevent Iran from closing Strait of Hormuz",
        content: "U.S. Secretary of State <PERSON> on Sunday called for China to prevent Iran from closing the Strait of Hormuz, one of the most important trade routes for crude oil in the world. Iran's foreign minister warned earlier Sunday that the Islamic Republic reserves all options to defend its sovereignty after U.S. airstrikes.",
        source: "CNBC",
        publishedAt: new Date().toISOString(),
        category: "general",
        url: "https://example.com/strait-hormuz"
      }
    ];

    console.log('📰 Testing with geopolitical articles:');
    testArticles.forEach((article, index) => {
      console.log(`  ${index + 1}. ${article.title.substring(0, 60)}...`);
    });

    console.log('\n🔍 Calling enhanced analyze endpoint...');
    const response = await axios.post('http://localhost:3001/api/discovery/analyze', {
      articles: testArticles
    });

    console.log('\n✅ Analysis completed successfully!');
    console.log('📊 Results Summary:');
    console.log(`  • Total Articles: ${response.data.data.summary.totalArticles}`);
    console.log(`  • Analyzed Articles: ${response.data.data.summary.analyzedArticles}`);
    console.log(`  • Unique Tickers: ${response.data.data.summary.uniqueTickers}`);
    console.log(`  • Affected Sectors: ${response.data.data.summary.affectedSectors}`);
    console.log(`  • Trading Opportunities: ${response.data.data.summary.tradingOpportunities}`);

    if (response.data.data.insights.extractedTickers.length > 0) {
      console.log('\n🎯 Discovered Tickers:');
      response.data.data.insights.extractedTickers.forEach(ticker => {
        console.log(`  • ${ticker.ticker}: Impact ${ticker.impactScore.toFixed(1)}, Confidence ${ticker.confidence.toFixed(1)}%, Opportunities ${ticker.opportunities}`);
      });
    } else {
      console.log('\n❌ No tickers discovered - this indicates an issue with the pipeline');
    }

    if (Object.keys(response.data.data.insights.sectorSummary).length > 0) {
      console.log('\n🏭 Sector Impact Summary:');
      Object.entries(response.data.data.insights.sectorSummary).forEach(([sector, data]) => {
        console.log(`  • ${sector}: Impact ${data.totalImpact.toFixed(1)}, Confidence ${data.avgConfidence.toFixed(1)}%`);
      });
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 Enhanced Discovery Pipeline Test Complete!');

    if (response.data.data.summary.uniqueTickers > 0) {
      console.log('✅ SUCCESS: The enhanced pipeline is working correctly!');
      console.log('   Geopolitical events are being properly analyzed and tickers identified.');
    } else {
      console.log('❌ ISSUE: No tickers were identified despite geopolitical content.');
      console.log('   This suggests there may still be an issue in the analysis pipeline.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testEnhancedDiscovery();
