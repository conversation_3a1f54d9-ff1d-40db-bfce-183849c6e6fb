// Test if the API key is available
const fs = require('fs');
const path = require('path');

// Try to load .env file
try {
  const envPath = path.join(__dirname, 'server', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  }
} catch (error) {
  console.log('Could not load .env file');
}

console.log('🔑 API Key Check');
console.log('=' .repeat(30));

const googleApiKey = process.env.GOOGLE_AI_API_KEY;
if (googleApiKey) {
  console.log('✅ GOOGLE_AI_API_KEY found');
  console.log(`   Length: ${googleApiKey.length} characters`);
  console.log(`   Starts with: ${googleApiKey.substring(0, 10)}...`);
} else {
  console.log('❌ GOOGLE_AI_API_KEY not found');
}

const newsApiKey = process.env.NEWS_API_KEY;
if (newsApiKey) {
  console.log('✅ NEWS_API_KEY found');
  console.log(`   Length: ${newsApiKey.length} characters`);
} else {
  console.log('❌ NEWS_API_KEY not found');
}

// Test the Google AI API directly
if (googleApiKey) {
  console.log('\n🤖 Testing Google AI API directly...');
  
  const { GoogleGenerativeAI } = require('./server/dist/services/NewsImpactAnalysisService');
  
  // This is a simple test - we'll just check if we can create the service
  try {
    console.log('✅ Google AI service can be imported');
  } catch (error) {
    console.log('❌ Error importing Google AI service:', error.message);
  }
}
