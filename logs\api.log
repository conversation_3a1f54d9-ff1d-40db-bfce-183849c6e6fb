[2025-06-24 00:10:26.604] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","category":"world"}
[2025-06-24 00:10:26.608] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","contentLength":346,"contentPreview":"iran accuses us of 'waging war' under 'absurd pretext' iran has accused the united states of waging war under an absurd pretext following recent military actions in the region. the iranian government "}
[2025-06-24 00:10:26.612] [INFO] [General]: ✅ CONFLICT pattern matched! | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","matchedPatterns":["iran.*military"]}
[2025-06-24 00:10:26.613] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"conflict","severity":"high","region":"middle east","entities":["IRAN"]}
[2025-06-24 00:10:26.614] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":3,"overallImpact":38,"confidence":85,"totalTickers":18}
[2025-06-24 00:10:26.620] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"U.S. calls on China to prevent Iran from closing Strait of Hormuz","category":"world"}
[2025-06-24 00:10:26.620] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"U.S. calls on China to prevent Iran from closing Strait of Hormuz","contentLength":401,"contentPreview":"u.s. calls on china to prevent iran from closing strait of hormuz the united states has called on china to use its influence to prevent iran from closing the strategic strait of hormuz shipping lane. "}
[2025-06-24 00:10:26.622] [INFO] [General]: ✅ ENERGY INFRASTRUCTURE pattern matched! | {"service":"trading-bot","title":"U.S. calls on China to prevent Iran from closing Strait of Hormuz","matchedPatterns":["strait.*hormuz","hormuz","petroleum","iran.*oil","iran.*energy"]}
[2025-06-24 00:10:26.622] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"energy_infrastructure","severity":"low","region":"global","entities":["IRAN","CHINA"]}
[2025-06-24 00:10:26.623] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":2,"overallImpact":21,"confidence":75,"totalTickers":14}
[2025-06-24 00:10:26.629] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"The US military used a 30,000-pound bunker-buster bomb in Middle East operation","category":"world"}
[2025-06-24 00:10:26.630] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"The US military used a 30,000-pound bunker-buster bomb in Middle East operation","contentLength":422,"contentPreview":"the us military used a 30,000-pound bunker-buster bomb in middle east operation the united states military deployed a massive 30,000-pound bunker-buster bomb in a recent middle east operation. the gbu"}
[2025-06-24 00:10:26.630] [INFO] [General]: ✅ CONFLICT pattern matched! | {"service":"trading-bot","title":"The US military used a 30,000-pound bunker-buster bomb in Middle East operation","matchedPatterns":["bunker.*buster"]}
[2025-06-24 00:10:26.630] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"conflict","severity":"low","region":"middle east","entities":[]}
[2025-06-24 00:10:26.631] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":3,"overallImpact":38,"confidence":75,"totalTickers":18}
[2025-06-24 00:12:43.603] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","category":"world"}
[2025-06-24 00:12:43.609] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","contentLength":432,"contentPreview":"iran accuses us of 'waging war' under 'absurd pretext' iran has accused the united states of waging war under an absurd pretext following recent military actions in the region. the iranian government "}
[2025-06-24 00:12:43.612] [INFO] [General]: ✅ CONFLICT pattern matched! | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","matchedPatterns":["iran.*military"]}
[2025-06-24 00:12:43.614] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"conflict","severity":"high","region":"middle east","entities":["IRAN"]}
[2025-06-24 00:12:43.615] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":3,"overallImpact":38,"confidence":85,"totalTickers":18}
[2025-06-24 00:12:43.616] [INFO] [General]: 🌍 Enhanced geopolitical analysis available | {"service":"trading-bot","eventType":"conflict","sectorsIdentified":3,"overallImpact":38}
[2025-06-24 00:13:30.922] [INFO] [General]: Raw AI response received | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","rawText":"```json\n{\n  \"impactScore\": 85,\n  \"impactDirection\": \"positive\",\n  \"timeframe\": \"short_term\",\n  \"confidence\": 90,\n  \"affectedSectors\": [\n    {\n      \"sector\": \"Energy\",\n      \"impactScore\": 75,\n      \"reasoning\": \"Escalating tensions between Iran and the US, particularly accusations of 'waging war,' immediately raise concerns about oil supply disruptions in the Middle East. The region is critical for global oil production, and any conflict could lead to reduced output or blocked shipping lanes, s..."}
[2025-06-24 00:13:30.923] [INFO] [General]: Market impact analysis completed successfully | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","impactScore":85,"affectedSectors":5,"opportunities":3}
[2025-06-24 00:22:56.500] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","category":"general"}
[2025-06-24 00:22:56.504] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","contentLength":400,"contentPreview":"iran accuses us of 'waging war' under 'absurd pretext' iran's representative to the un security council has accused the us of having waged a war against iran under an absurd pretext following recent m"}
[2025-06-24 00:22:56.507] [INFO] [General]: ✅ CONFLICT pattern matched! | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","matchedPatterns":["iran.*military"]}
[2025-06-24 00:22:56.508] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"conflict","severity":"high","region":"middle east","entities":["IRAN"]}
[2025-06-24 00:22:56.509] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":3,"overallImpact":38,"confidence":85,"totalTickers":18}
[2025-06-24 00:22:56.512] [INFO] [General]: 🌍 Starting geopolitical event analysis | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","category":"general"}
[2025-06-24 00:22:56.513] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","contentLength":400,"contentPreview":"iran accuses us of 'waging war' under 'absurd pretext' iran's representative to the un security council has accused the us of having waged a war against iran under an absurd pretext following recent m"}
[2025-06-24 00:22:56.514] [INFO] [General]: ✅ CONFLICT pattern matched! | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","matchedPatterns":["iran.*military"]}
[2025-06-24 00:22:56.514] [INFO] [General]: 🎯 Geopolitical event classified | {"service":"trading-bot","type":"conflict","severity":"high","region":"middle east","entities":["IRAN"]}
[2025-06-24 00:22:56.515] [INFO] [General]: ✅ Geopolitical analysis completed | {"service":"trading-bot","sectorsAffected":3,"overallImpact":38,"confidence":85,"totalTickers":18}
[2025-06-24 00:22:56.515] [INFO] [General]: 🌍 Enhanced geopolitical analysis available | {"service":"trading-bot","eventType":"conflict","sectorsIdentified":3,"overallImpact":38}
[2025-06-24 00:22:56.517] [INFO] [General]: 🤖 Sending prompt to AI model | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","promptLength":7779,"hasGeopoliticalContext":true}
[2025-06-24 00:23:41.745] [INFO] [General]: 🤖 AI model response received | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","responseLength":11156,"responsePreview":"```json\n{\n  \"impactScore\": 78,\n  \"impactDirection\": \"positive\",\n  \"timeframe\": \"short_term\",\n  \"confidence\": 85,\n  \"affectedSectors\": [\n    {\n      \"sector\": \"Energy\",\n      \"impactScore\": 85,\n      \""}
[2025-06-24 00:23:41.751] [INFO] [General]: Raw AI response received | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","rawText":"```json\n{\n  \"impactScore\": 78,\n  \"impactDirection\": \"positive\",\n  \"timeframe\": \"short_term\",\n  \"confidence\": 85,\n  \"affectedSectors\": [\n    {\n      \"sector\": \"Energy\",\n      \"impactScore\": 85,\n      \"reasoning\": \"Escalating tensions between Iran and the US, particularly accusations of 'waging war,' immediately raise concerns about potential disruptions to oil supply. The Middle East is a critical oil-producing region, and Iran controls strategic waterways like the Strait of Hormuz, a chokepoint ..."}
[2025-06-24 00:23:41.752] [INFO] [General]: Market impact analysis completed successfully | {"service":"trading-bot","title":"Iran accuses US of 'waging war' under 'absurd pretext'","impactScore":78,"affectedSectors":5,"opportunities":2}
[2025-06-24 01:39:27.015] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:40:19.047] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:41:24.220] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:41:26.652] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "manufacturing",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "Oil producers (e.g., ExxonMobil, Chevron)",
      "Airlines",
      "Shipping companies",
      "Automobile manufacturers"
    ],
    "reasoning": "Rising oil prices increase production costs for many industries and lead to higher consumer prices, impacting profitability and consumer spending. Conflict is the catalyst.",
    "tradingOpportunities": [
      "Short positions on airlines and other transportation stocks",
      "Long positions on oil producers"
    ],
    "riskFactors": [
      "Inflation",
      "Economic slowdown",
      "Reduced consumer spending"
    ],
    "catalysts": [
      "Conflict",
      "Geopolitical instability"
    ],
    "geopoliticalContext": "Conflict leading to supply disruptions and price volatility in the global oil market."
  }
]
```}
[2025-06-24 01:41:42.094] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:41:44.203] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 85,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "Oil companies",
      "Airlines",
      "Shipping companies"
    ],
    "reasoning": "Rising oil prices generally lead to higher production costs for many industries and directly impact consumer spending.  The conflict is the primary driver, creating supply chain disruptions and uncertainty.",
    "tradingOpportunities": [
      "Short oil stocks",
      "Buy stocks in alternative energy companies"
    ],
    "riskFactors": [
      "Inflation",
      "Economic slowdown",
      "Increased input costs"
    ],
    "catalysts": [
      "Escalation of conflict",
      "Further supply disruptions",
      "Increased demand (e.g., winter season)"
    ],
    "geopoliticalContext": "Conflict causing supply chain issues, impacting global energy markets and potentially fueling economic instability."
  }
]
```}
[2025-06-24 01:42:50.673] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:42:53.155] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "Oil companies",
      "Airlines",
      "Shipping companies"
    ],
    "reasoning": "Rising oil prices, driven by conflict, will increase the cost of production and consumption, negatively impacting related sectors and companies.",
    "tradingOpportunities": [
      "Short oil companies",
      "Short airline stocks",
      "Invest in companies that benefit from higher oil prices (e.g., alternative energy)."
    ],
    "riskFactors": [
      "Increased inflation",
      "Decreased consumer spending",
      "Economic slowdown"
    ],
    "catalysts": [
      "Escalation of conflict",
      "Further supply disruptions",
      "Unexpected demand surges"
    ],
    "geopoliticalContext": "Conflict is the primary driver, implying instability and potential for further escalation."
  }
]
```}
[2025-06-24 01:44:51.608] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:44:54.619] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "ExxonMobil",
      "Chevron",
      "Shell",
      "airlines",
      "delivery services"
    ],
    "reasoning": "Rising oil prices typically lead to increased costs for businesses and consumers, impacting profitability and disposable income. Conflict is a key driver, creating uncertainty in supply.",
    "tradingOpportunities": [
      "Short oil-dependent stocks",
      "Long energy producers",
      "Consider shorting consumer discretionary stocks"
    ],
    "riskFactors": [
      "Geopolitical instability",
      "Inflation",
      "Supply chain disruptions"
    ],
    "catalysts": [
      "Escalation of conflict",
      "Further supply disruptions",
      "Increased demand"
    ],
    "geopoliticalContext": "Conflict causing supply concerns and price volatility is a key driver. The location of the conflict and involved parties will heavily influence the specific economic impacts and opportunities."
  }
]
```}
[2025-06-24 01:45:58.266] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:46:01.213] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "ExxonMobil",
      "Chevron",
      "Shell",
      "United Airlines",
      "Delta Air Lines",
      "Ford",
      "General Motors"
    ],
    "reasoning": "Rising oil prices directly increase costs for energy sector companies and for businesses that are heavily reliant on transportation (e.g., airlines). This will also negatively affect consumer spending on discretionary items if energy costs increase household budgets. The conflict is the catalyst for this negative impact. ",
    "tradingOpportunities": [
      "Short energy-intensive sectors (e.g., airlines).",
      "Long oil and gas exploration and production companies."
    ],
    "riskFactors": [
      "Inflation",
      "Reduced consumer spending",
      "Supply chain disruptions related to the conflict."
    ],
    "catalysts": [
      "Conflict",
      "Supply chain disruptions"
    ],
    "geopoliticalContext": "Conflict is causing supply concerns and price increases in the oil market. This will impact global trade and economic stability."
  }
]
```}
[2025-06-24 01:47:32.160] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:47:34.757] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer discretionary"
    ],
    "affectedCompanies": [
      "Oil producers",
      "Airlines",
      "Shipping companies"
    ],
    "reasoning": "Rising oil prices will increase costs for consumers and businesses, impacting profitability and potentially slowing economic growth. The conflict is the primary driver.",
    "tradingOpportunities": [
      "Short oil producers",
      "Short airlines",
      "Short consumer discretionary stocks"
    ],
    "riskFactors": [
      "Increased inflation",
      "Reduced consumer spending",
      "Economic slowdown"
    ],
    "catalysts": [
      "Conflict escalation",
      "Further supply disruptions"
    ],
    "geopoliticalContext": "The article references conflict, likely in an oil-producing region, creating supply uncertainty and driving up prices."
  }
]
```}
[2025-06-24 01:48:53.770] [INFO] [General]: Starting batch analysis with enhanced rate limiting | {service=trading-bot, totalArticles=2, maxConcurrent=1, model=gemini-2.0-flash-lite}
[2025-06-24 01:48:56.544] [ERROR] [General]: Failed to parse batch LLM response | {service=trading-bot, text=```json
[
  {
    "impactScore": 75,
    "impactDirection": "negative",
    "timeframe": "immediate",
    "confidence": 90,
    "affectedSectors": [
      "energy",
      "transportation",
      "consumer goods"
    ],
    "affectedCompanies": [
      "Oil companies",
      "Airlines",
      "Shipping companies"
    ],
    "reasoning": "Rising oil prices directly increase costs for energy-intensive sectors, impacting profitability and consumer spending. Conflict is a key driver of price increases, suggesting volatility and supply chain disruptions.",
    "tradingOpportunities": [
      "Short oil stocks",
      "Long defensive consumer stocks",
      "Consider hedging strategies for airlines and shipping companies"
    ],
    "riskFactors": [
      "Inflation",
      "Economic slowdown",
      "Supply chain disruption",
      "Geopolitical instability"
    ],
    "catalysts": [
      "Escalation of conflict",
      "Production cuts",
      "Increased demand"
    ],
    "geopoliticalContext": "Conflict is the primary driver, which can include regional wars or broader geopolitical tensions affecting energy supplies and global markets."
  }
]
```}
[2025-06-24 01:49:53.735] [INFO] [General]: 🌍 Starting geopolitical event analysis | {service=trading-bot, title=Test Article 1, category=energy}
[2025-06-24 01:49:53.737] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {service=trading-bot, title=Test Article 1, contentLength=78, contentPreview=test article 1 this is a test content about oil prices rising due to conflict.}
[2025-06-24 01:49:53.740] [INFO] [General]: ✅ ENERGY INFRASTRUCTURE pattern matched! | {service=trading-bot, title=Test Article 1, matchedPatterns=["oil.*price"]}
[2025-06-24 01:49:53.741] [INFO] [General]: 🎯 Geopolitical event classified | {service=trading-bot, type=energy_infrastructure, severity=low, region=global, entities=[]}
[2025-06-24 01:49:53.742] [INFO] [General]: ✅ Geopolitical analysis completed | {service=trading-bot, sectorsAffected=2, overallImpact=21, confidence=75, totalTickers=14}
[2025-06-24 01:50:11.426] [INFO] [General]: ✅ AI market impact analysis completed | {service=trading-bot, title=Test Article 1..., processingTime=17694ms, model=gemini-2.0-flash-lite, estimatedTokens=521}
[2025-06-24 01:50:53.740] [INFO] [General]: 🌍 Starting geopolitical event analysis | {service=trading-bot, title=Test Article 2, category=defense}
[2025-06-24 01:50:53.742] [INFO] [General]: 🔍 Analyzing article for geopolitical patterns | {service=trading-bot, title=Test Article 2, contentLength=75, contentPreview=test article 2 defense stocks surge after new military contracts announced.}
[2025-06-24 01:50:53.745] [INFO] [General]: ✅ MILITARY ACTION pattern matched! | {service=trading-bot, title=Test Article 2, matchedPatterns=["defense.*contract"]}
[2025-06-24 01:50:53.746] [INFO] [General]: 🎯 Geopolitical event classified | {service=trading-bot, type=military_action, severity=low, region=global, entities=[]}
[2025-06-24 01:50:53.746] [INFO] [General]: ✅ Geopolitical analysis completed | {service=trading-bot, sectorsAffected=1, overallImpact=45, confidence=90, totalTickers=8}
[2025-06-24 01:51:11.239] [INFO] [General]: ✅ AI market impact analysis completed | {service=trading-bot, title=Test Article 2..., processingTime=59793ms, model=gemini-2.0-flash-lite, estimatedTokens=521}
