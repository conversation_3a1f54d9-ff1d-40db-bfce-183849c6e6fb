import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, Zap, AlertCircle, Loader, TrendingUp, Building2, Target, Clock } from 'lucide-react';
import { socketService } from '../services/socket';
import { api } from '../services/api';
import { DiscoveredTicker } from '../types';
import { Alert, AlertTitle, AlertDescription } from '../components/ui/alert';
import { Button } from '../components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '../components/ui/table';

interface DiscoveryEvent {
  stage: string;
  message: string;
  data?: any;
}

const STAGES = [
  { id: 'start', title: 'Initiating Discovery' },
  { id: 'news_fetch', title: 'Fetching Global News' },
  { id: 'news_filter', title: 'Filtering Relevant Articles' },
  { id: 'ai_analysis', title: 'AI Market Impact Analysis' },
  { id: 'ticker_extraction', title: 'Extracting Affected Tickers' },
  { id: 'ticker_processing', title: 'Processing Discovered Tickers' },
  { id: 'database_save', title: 'Saving to Database' },
  { id: 'complete', title: 'Discovery Complete' },
];

const Discovery: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<DiscoveryEvent[]>([]);
  const [results, setResults] = useState<DiscoveredTicker[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [currentStage, setCurrentStage] = useState<string>('');

  useEffect(() => {
    socketService.connect();

    const handleUpdate = (data: DiscoveryEvent) => {
      setProgress(prev => [...prev, data]);
      setCurrentStage(data.stage);
      if (data.stage === 'complete') {
        // Results will be fetched from the API response, not WebSocket
        setIsRunning(false);
      }
    };

    const handleError = (data: { message: string }) => {
      setError(data.message);
      setIsRunning(false);
    };

    socketService.on('discovery_update', handleUpdate);
    socketService.on('discovery_error', handleError);

    return () => {
      socketService.off('discovery_update', handleUpdate);
      socketService.off('discovery_error', handleError);
      socketService.disconnect();
    };
  }, []);

  const handleRunDiscovery = async () => {
    setIsRunning(true);
    setProgress([]);
    setResults([]);
    setError(null);
    setCurrentStage('start');
    try {
      const response = await api.get('/discovery/tickers');
      if (response.data.success && response.data.data.tickers) {
        setResults(response.data.data.tickers);
        setCurrentStage('complete');
        setIsRunning(false);
      }
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      setIsRunning(false);
    }
  };

  const handleCancel = () => {
    // In a real scenario, you'd want an API call to gracefully stop the backend process
    setIsRunning(false);
    setError('Discovery process cancelled by user.');
  };

  const getStageStatusIcon = (stageId: string) => {
    const stageIndex = STAGES.findIndex(s => s.id === stageId);
    const currentStageIndex = STAGES.findIndex(s => s.id === currentStage);

    if (currentStageIndex > stageIndex) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (currentStageIndex === stageIndex && isRunning) return <Loader className="h-5 w-5 animate-spin text-blue-500" />;
    if (error && currentStageIndex === stageIndex) return <AlertCircle className="h-5 w-5 text-red-500" />;
    return <CheckCircle className="h-5 w-5 text-gray-300" />;
  };

  return (
    <div className="container mx-auto p-4 space-y-8">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center">
          <Zap className="mr-3 h-8 w-8 text-blue-500" />
          <h1 className="text-3xl font-bold text-gray-900">AI-Powered Ticker Discovery</h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Leverage advanced AI to scan global news, identify market-moving events, and uncover new investment opportunities automatically.
        </p>
      </div>

      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Discovery Control Panel</span>
            {results.length > 0 && (
              <div className="text-sm text-gray-500">
                Last run: {new Date().toLocaleString()}
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-gray-600">
                {isRunning ? 'Discovery in progress...' : 'Ready to discover new trading opportunities'}
              </p>
              {results.length > 0 && (
                <div className="flex space-x-4 text-sm">
                  <span className="text-green-600 font-medium">{results.length} tickers discovered</span>
                  <span className="text-blue-600">
                    {results.filter(t => t.riskLevel === 'low').length} low risk
                  </span>
                  <span className="text-yellow-600">
                    {results.filter(t => t.riskLevel === 'medium').length} medium risk
                  </span>
                  <span className="text-red-600">
                    {results.filter(t => t.riskLevel === 'high').length} high risk
                  </span>
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              {!isRunning ? (
                <Button onClick={handleRunDiscovery} className="bg-blue-600 hover:bg-blue-700">
                  <Zap className="mr-2 h-4 w-4" />
                  Run Discovery
                </Button>
              ) : (
                <Button onClick={handleCancel} variant="outline">
                  Cancel Discovery
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Discovery Process</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              {STAGES.map(stage => (
                <li key={stage.id} className="flex items-center space-x-3">
                  {getStageStatusIcon(stage.id)}
                  <span className="font-medium">{stage.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Live Progress</CardTitle>
          </CardHeader>
          <CardContent className="h-64 overflow-y-auto bg-gray-50 p-4 rounded-md">
            <AnimatePresence>
              {progress.map((event, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="mb-2 text-sm"
                >
                  <span className="font-semibold text-blue-600 mr-2">{`[${event.stage}]`}</span>
                  <span>{event.message}</span>
                </motion.div>
              ))}
            </AnimatePresence>
            {!isRunning && progress.length === 0 && !error && (
              <div className="text-center text-gray-500">
                Run the discovery process to see live updates.
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>An Error Occurred</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {results.length > 0 && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="mr-2 h-6 w-6 text-green-500" />
                Discovery Results ({results.length} tickers found)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {results.map((ticker, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-bold text-lg text-blue-600">{ticker.ticker}</h3>
                        <p className="text-sm text-gray-600 truncate">{ticker.companyName}</p>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${
                          ticker.impactScore > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          Impact: {ticker.impactScore}
                        </div>
                        <div className="text-sm text-gray-500">
                          {ticker.confidence}% confidence
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <Building2 className="mr-2 h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{ticker.sector}</span>
                      </div>

                      <div className="flex items-center text-sm">
                        <Target className="mr-2 h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          Risk: <span className={`font-medium ${
                            ticker.riskLevel === 'low' ? 'text-green-600' :
                            ticker.riskLevel === 'medium' ? 'text-yellow-600' : 'text-red-600'
                          }`}>{ticker.riskLevel}</span>
                        </span>
                      </div>

                      <div className="flex items-center text-sm">
                        <Clock className="mr-2 h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{ticker.timeframe}</span>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t">
                      <p className="text-xs text-gray-500 line-clamp-2">
                        {ticker.discoveryReason}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Detailed Analysis Table</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Ticker</TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Sector</TableHead>
                      <TableHead>Impact Score</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>Risk Level</TableHead>
                      <TableHead>Market Cap</TableHead>
                      <TableHead>Opportunities</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((ticker, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-bold text-blue-600">{ticker.ticker}</TableCell>
                        <TableCell className="max-w-xs truncate">{ticker.companyName}</TableCell>
                        <TableCell>{ticker.sector}</TableCell>
                        <TableCell>
                          <span className={`font-medium ${
                            ticker.impactScore > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {ticker.impactScore}
                          </span>
                        </TableCell>
                        <TableCell>{ticker.confidence}%</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            ticker.riskLevel === 'low' ? 'bg-green-100 text-green-800' :
                            ticker.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {ticker.riskLevel}
                          </span>
                        </TableCell>
                        <TableCell>
                          {ticker.marketCap ? `$${(ticker.marketCap / 1e9).toFixed(1)}B` : 'N/A'}
                        </TableCell>
                        <TableCell>{ticker.tradingOpportunities || 0}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Discovery;
