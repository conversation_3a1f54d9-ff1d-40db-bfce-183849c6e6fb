import dotenv from 'dotenv';
dotenv.config();

console.log('Starting Trading Bot Server...');

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import path from 'path';
import cron from 'node-cron';
import mongoose from 'mongoose';

import { connectDatabase } from './config/database';
import { SETTINGS } from './config/settings';
import { DataUpdateJob } from './jobs/dataUpdateJob';
import tickerRoutes from './routes/tickerRoutes';
import discoveryRoutes from './routes/discovery';
import { logger } from './utils/logger';
import { SentimentAnalysisService } from './services/SentimentAnalysisService';
import { initializeWebSocket } from './services/WebSocketService';
import { logError } from './utils/logger';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourdomain.com']
    : ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Compression
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API routes
app.use('/api/tickers', tickerRoutes);
app.use('/api/discovery', discoveryRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  });
});

// Logs endpoint
app.get('/api/logs', async (req, res) => {
  try {
    const { LogEntryModel } = await import('./models/LogEntry');
    const { limit = 50, type, ticker } = req.query;

    const filter: any = {};
    if (type) filter.type = type;
    if (ticker) filter.ticker = ticker;

    const logs = await LogEntryModel.find(filter)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string));

    res.json({
      success: true,
      data: logs,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error fetching logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch logs',
      timestamp: new Date()
    });
  }
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/dist')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  });
}

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    timestamp: new Date()
  });
});

let server: import('http').Server;

// Start server
const startServer = async () => {
  try {
    // Connect to database
    await connectDatabase();

    // Start the server
    const port = SETTINGS.port;
    server = app.listen(port, () => {
      logger.info(`Server running on port ${port}`);
      logger.info(`Environment: ${SETTINGS.nodeEnv}`);

      // Create sentiment analysis service with API key
      const sentimentAnalysisService = new SentimentAnalysisService(SETTINGS.googleAIApiKey);

      // Start the data update job
      const dataUpdateJob = new DataUpdateJob(sentimentAnalysisService);
      dataUpdateJob.start();
      logger.info('Data update job started');

      // Schedule the data update job to run every 15 minutes
      cron.schedule(SETTINGS.updateInterval, () => {
        dataUpdateJob.updateAllTickerData();
      }, {
        scheduled: true,
        timezone: 'America/New_York'
      });

      // Initialize WebSocket service
      initializeWebSocket(server);
    });

    server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${port} is already in use.`);
        process.exit(1);
      } else {
        logger.error(`An error occurred: ${error.message}`);
      }
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

const gracefulShutdown = async (signal: string) => {
  logger.info(`[SYSTEM] ${signal} received. Shutting down...`);
  if (server) {
    server.close(async () => {
      logger.info('[SYSTEM] HTTP server closed.');
      await mongoose.connection.close();
      logger.info('[SYSTEM] MongoDB connection closed.');
      logger.on('finish', () => process.exit(0));
      logger.end();
    });
  } else {
    await mongoose.connection.close();
    logger.info('[SYSTEM] MongoDB connection closed.');
    logger.on('finish', () => process.exit(0));
    logger.end();
  }
};

// Handle uncaught exceptions and rejections
const handleFatalError = (err: Error) => {
  logger.error({
    message: `FATAL ERROR: ${err.message}`,
    stack: err.stack,
    component: 'SYSTEM',
  });
  logger.on('finish', () => process.exit(1));
  logger.end();
};

process.on('uncaughtException', handleFatalError);

process.on('unhandledRejection', (reason, promise) => {
  const err = reason as Error;
  logger.error({
    message: `Unhandled Rejection at: ${promise}`,
    reason: err.message,
    stack: err.stack,
    component: 'SYSTEM',
  });
  logger.on('finish', () => process.exit(1));
  logger.end();
});

// Handle OS signals for graceful shutdown
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

startServer(); 