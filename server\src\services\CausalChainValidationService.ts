import { logger } from '../utils/logger';
import { GlobalNewsArticle } from './GlobalNewsService';
import { CompanyProfile } from './CompanyProfileService';
import { MarketImpactAnalysis, CompanyImpact } from './NewsImpactAnalysisService';

export interface CausalLink {
  from: string;
  to: string;
  mechanism: string;
  confidence: number; // 0-100
  reasoning: string;
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  evidenceStrength: 'weak' | 'moderate' | 'strong';
}

export interface CausalChain {
  ticker: string;
  companyName: string;
  newsEvent: string;
  links: CausalLink[];
  overallConfidence: number; // Product of individual link confidences
  overallStrength: 'weak' | 'moderate' | 'strong';
  isValid: boolean;
  validationReasoning: string;
  riskFactors: string[];
}

export interface CausalValidationResult {
  ticker: string;
  isValid: boolean;
  causalChain: CausalChain;
  validationScore: number; // 0-100
  criticalFlaws: string[];
  strengthFactors: string[];
}

export class CausalChainValidationService {
  private static instance: CausalChainValidationService;
  private readonly MIN_CONFIDENCE_THRESHOLD = 30; // Reduced for geopolitical events
  private readonly MIN_LINK_CONFIDENCE = 25; // Reduced for indirect but real impacts

  public static getInstance(): CausalChainValidationService {
    if (!CausalChainValidationService.instance) {
      CausalChainValidationService.instance = new CausalChainValidationService();
    }
    return CausalChainValidationService.instance;
  }

  /**
   * Validate causal chain for a ticker-news combination
   */
  public async validateCausalChain(
    article: GlobalNewsArticle,
    profile: CompanyProfile,
    companyImpact: CompanyImpact
  ): Promise<CausalValidationResult> {
    try {
      // Build the causal chain
      const causalChain = await this.buildCausalChain(article, profile, companyImpact);
      
      // Validate the chain
      const validationResult = this.validateChain(causalChain);
      
      // Calculate overall validation score
      const validationScore = this.calculateValidationScore(causalChain, validationResult);

      return {
        ticker: profile.ticker,
        isValid: validationResult.isValid,
        causalChain,
        validationScore,
        criticalFlaws: validationResult.criticalFlaws,
        strengthFactors: validationResult.strengthFactors
      };
    } catch (error) {
      logger.error(`Error validating causal chain for ${profile.ticker}:`, error);
      return this.getFailedValidationResult(profile.ticker, error as Error);
    }
  }

  /**
   * Build causal chain from news to market impact
   */
  private async buildCausalChain(
    article: GlobalNewsArticle,
    profile: CompanyProfile,
    companyImpact: CompanyImpact
  ): Promise<CausalChain> {
    const links: CausalLink[] = [];

    // Link 1: News Event → Business Impact
    const businessLink = this.createBusinessImpactLink(article, profile, companyImpact);
    links.push(businessLink);

    // Link 2: Business Impact → Financial Impact
    const financialLink = this.createFinancialImpactLink(profile, companyImpact);
    links.push(financialLink);

    // Link 3: Financial Impact → Market Reaction
    const marketLink = this.createMarketImpactLink(profile, companyImpact);
    links.push(marketLink);

    // Calculate overall confidence
    const overallConfidence = this.calculateOverallConfidence(links);
    const overallStrength = this.determineOverallStrength(links);

    return {
      ticker: profile.ticker,
      companyName: profile.companyName,
      newsEvent: article.title,
      links,
      overallConfidence,
      overallStrength,
      isValid: overallConfidence >= this.MIN_CONFIDENCE_THRESHOLD,
      validationReasoning: this.generateValidationReasoning(links, overallConfidence),
      riskFactors: this.identifyRiskFactors(article, profile, links)
    };
  }

  /**
   * Create business impact link
   */
  private createBusinessImpactLink(
    article: GlobalNewsArticle,
    profile: CompanyProfile,
    companyImpact: CompanyImpact
  ): CausalLink {
    const newsContent = `${article.title} ${article.content}`.toLowerCase();
    
    // Analyze how news affects business operations
    let confidence = 50; // Base confidence
    let mechanism = 'General business environment impact';
    let reasoning = 'News may have indirect impact on business operations';
    let evidenceStrength: 'weak' | 'moderate' | 'strong' = 'weak';

    // Check for direct business model relevance
    const businessKeywords = profile.businessModel.toLowerCase().split(' ');
    const directMatches = businessKeywords.filter(keyword => 
      keyword.length > 3 && newsContent.includes(keyword)
    );

    if (directMatches.length > 0) {
      confidence += 20;
      mechanism = `Direct impact on business model: ${directMatches.join(', ')}`;
      reasoning = `News directly mentions aspects of company's business model`;
      evidenceStrength = 'moderate';
    }

    // Check for revenue stream impact
    for (const stream of profile.revenueStreams) {
      const streamKeywords = stream.source.toLowerCase().split(' ');
      const streamMatches = streamKeywords.filter(keyword => 
        keyword.length > 3 && newsContent.includes(keyword)
      );
      
      if (streamMatches.length > 0) {
        confidence += 15 * (stream.percentage / 100);
        mechanism = `Impact on revenue stream: ${stream.source} (${stream.percentage}% of revenue)`;
        reasoning = `News affects ${stream.source}, which represents ${stream.percentage}% of company revenue`;
        evidenceStrength = stream.percentage > 50 ? 'strong' : 'moderate';
        break;
      }
    }

    // Check for regulatory impact
    for (const regBody of profile.keyDependencies.regulatoryBodies) {
      if (newsContent.includes(regBody.toLowerCase())) {
        confidence += 25;
        mechanism = `Regulatory impact via ${regBody}`;
        reasoning = `News involves ${regBody}, a key regulatory body for the company`;
        evidenceStrength = 'strong';
        break;
      }
    }

    // Check for geographic relevance
    for (const region of profile.geographicExposure.keyRegions) {
      if (newsContent.includes(region.toLowerCase())) {
        const exposureWeight = region === 'United States' ? 
          profile.geographicExposure.domestic : 
          profile.geographicExposure.international;
        confidence += 10 * (exposureWeight / 100);
        mechanism += ` Geographic exposure to ${region}`;
        reasoning += ` Company has ${exposureWeight}% exposure to ${region}`;
        break;
      }
    }

    // Cap confidence at 95
    confidence = Math.min(95, confidence);

    return {
      from: 'News Event',
      to: 'Business Impact',
      mechanism,
      confidence,
      reasoning,
      timeframe: this.determineTimeframe(article, 'business'),
      evidenceStrength
    };
  }

  /**
   * Create financial impact link
   */
  private createFinancialImpactLink(
    profile: CompanyProfile,
    companyImpact: CompanyImpact
  ): CausalLink {
    let confidence = 60; // Base confidence for financial translation
    let mechanism = 'Business impact translates to financial performance';
    let reasoning = 'Business changes typically affect financial metrics';
    let evidenceStrength: 'weak' | 'moderate' | 'strong' = 'moderate';

    // Analyze impact score magnitude
    const impactMagnitude = Math.abs(companyImpact.impactScore);
    if (impactMagnitude > 70) {
      confidence += 20;
      mechanism = 'High-magnitude business impact with clear financial implications';
      reasoning = `Impact score of ${companyImpact.impactScore} suggests significant financial effect`;
      evidenceStrength = 'strong';
    } else if (impactMagnitude > 40) {
      confidence += 10;
      mechanism = 'Moderate business impact with measurable financial implications';
      reasoning = `Impact score of ${companyImpact.impactScore} suggests moderate financial effect`;
    } else {
      confidence -= 10;
      mechanism = 'Low-magnitude business impact with uncertain financial implications';
      reasoning = `Impact score of ${companyImpact.impactScore} suggests limited financial effect`;
      evidenceStrength = 'weak';
    }

    // Consider company size and market cap
    if (profile.marketCap > ***********) { // > $10B
      confidence += 5; // Large companies have more predictable financial impacts
      reasoning += '; Large market cap provides financial stability';
    } else if (profile.marketCap < 1000000000) { // < $1B
      confidence += 10; // Small companies may have more volatile financial impacts
      reasoning += '; Small market cap may amplify financial impact';
    }

    // Consider sector-specific financial sensitivity
    const sectorSensitivity = this.getSectorFinancialSensitivity(profile.sector);
    confidence += sectorSensitivity.adjustment;
    reasoning += `; ${sectorSensitivity.reasoning}`;

    confidence = Math.min(95, Math.max(20, confidence));

    return {
      from: 'Business Impact',
      to: 'Financial Impact',
      mechanism,
      confidence,
      reasoning,
      timeframe: 'short_term',
      evidenceStrength
    };
  }

  /**
   * Create market impact link
   */
  private createMarketImpactLink(
    profile: CompanyProfile,
    companyImpact: CompanyImpact
  ): CausalLink {
    let confidence = 70; // Base confidence for market reaction
    let mechanism = 'Financial impact drives market sentiment and stock price';
    let reasoning = 'Financial performance changes typically affect stock valuation';
    let evidenceStrength: 'weak' | 'moderate' | 'strong' = 'moderate';

    // Consider market cap and liquidity
    if (profile.marketCap > *********00) { // > $50B
      confidence += 10;
      mechanism = 'Large-cap stock with high market attention and liquidity';
      reasoning = 'Large market cap ensures market attention and price discovery';
      evidenceStrength = 'strong';
    } else if (profile.marketCap < *********) { // < $500M
      confidence -= 10;
      mechanism = 'Small-cap stock with limited market attention';
      reasoning = 'Small market cap may limit market reaction despite financial impact';
      evidenceStrength = 'weak';
    }

    // Consider sector market behavior
    const sectorBehavior = this.getSectorMarketBehavior(profile.sector);
    confidence += sectorBehavior.adjustment;
    reasoning += `; ${sectorBehavior.reasoning}`;

    // Consider impact direction and magnitude
    if (Math.abs(companyImpact.impactScore) > 60) {
      confidence += 10;
      mechanism += ' with high-magnitude impact';
      reasoning += `; High impact score (${companyImpact.impactScore}) likely to drive market reaction`;
    }

    confidence = Math.min(95, Math.max(30, confidence));

    return {
      from: 'Financial Impact',
      to: 'Market Reaction',
      mechanism,
      confidence,
      reasoning,
      timeframe: 'immediate',
      evidenceStrength
    };
  }

  /**
   * Validate the complete causal chain
   */
  private validateChain(chain: CausalChain): {
    isValid: boolean;
    criticalFlaws: string[];
    strengthFactors: string[];
  } {
    const criticalFlaws: string[] = [];
    const strengthFactors: string[] = [];

    // Check minimum confidence for each link
    for (const link of chain.links) {
      if (link.confidence < this.MIN_LINK_CONFIDENCE) {
        criticalFlaws.push(`${link.from} → ${link.to} link has low confidence (${link.confidence}%)`);
      } else if (link.confidence > 80) {
        strengthFactors.push(`Strong ${link.from} → ${link.to} connection (${link.confidence}%)`);
      }
    }

    // Check overall confidence
    if (chain.overallConfidence < this.MIN_CONFIDENCE_THRESHOLD) {
      criticalFlaws.push(`Overall causal chain confidence too low (${chain.overallConfidence}%)`);
    }

    // Check for logical consistency
    const logicalFlaws = this.checkLogicalConsistency(chain);
    criticalFlaws.push(...logicalFlaws);

    // Check for evidence strength
    const weakLinks = chain.links.filter(link => link.evidenceStrength === 'weak');
    if (weakLinks.length > 1) {
      criticalFlaws.push(`Multiple weak evidence links: ${weakLinks.map(l => `${l.from}→${l.to}`).join(', ')}`);
    }

    const strongLinks = chain.links.filter(link => link.evidenceStrength === 'strong');
    if (strongLinks.length > 0) {
      strengthFactors.push(`Strong evidence links: ${strongLinks.map(l => `${l.from}→${l.to}`).join(', ')}`);
    }

    return {
      isValid: criticalFlaws.length === 0 && chain.overallConfidence >= this.MIN_CONFIDENCE_THRESHOLD,
      criticalFlaws,
      strengthFactors
    };
  }

  /**
   * Check logical consistency of the causal chain
   */
  private checkLogicalConsistency(chain: CausalChain): string[] {
    const flaws: string[] = [];

    // Check timeframe consistency
    const timeframes = chain.links.map(link => link.timeframe);
    if (timeframes.includes('long_term') && timeframes.includes('immediate')) {
      flaws.push('Inconsistent timeframes: immediate and long-term impacts in same chain');
    }

    // Check mechanism plausibility
    for (const link of chain.links) {
      if (link.mechanism.includes('unclear') || link.mechanism.includes('unknown')) {
        flaws.push(`Unclear causal mechanism: ${link.from} → ${link.to}`);
      }
    }

    return flaws;
  }

  /**
   * Calculate overall confidence from individual links
   */
  private calculateOverallConfidence(links: CausalLink[]): number {
    if (links.length === 0) return 0;
    
    // Use geometric mean to ensure all links contribute
    const product = links.reduce((acc, link) => acc * (link.confidence / 100), 1);
    return Math.round(Math.pow(product, 1 / links.length) * 100);
  }

  /**
   * Determine overall strength from individual links
   */
  private determineOverallStrength(links: CausalLink[]): 'weak' | 'moderate' | 'strong' {
    const strongCount = links.filter(link => link.evidenceStrength === 'strong').length;
    const weakCount = links.filter(link => link.evidenceStrength === 'weak').length;

    if (strongCount >= 2) return 'strong';
    if (weakCount >= 2) return 'weak';
    return 'moderate';
  }

  /**
   * Generate validation reasoning
   */
  private generateValidationReasoning(links: CausalLink[], overallConfidence: number): string {
    const reasons: string[] = [];
    
    reasons.push(`Causal chain has ${links.length} links with ${overallConfidence}% overall confidence`);
    
    const strongLinks = links.filter(link => link.evidenceStrength === 'strong').length;
    const weakLinks = links.filter(link => link.evidenceStrength === 'weak').length;
    
    if (strongLinks > 0) {
      reasons.push(`${strongLinks} strong evidence links`);
    }
    if (weakLinks > 0) {
      reasons.push(`${weakLinks} weak evidence links`);
    }

    return reasons.join('; ');
  }

  /**
   * Identify risk factors in the causal chain
   */
  private identifyRiskFactors(
    article: GlobalNewsArticle,
    profile: CompanyProfile,
    links: CausalLink[]
  ): string[] {
    const riskFactors: string[] = [];

    // Time-based risks
    const immediateLinks = links.filter(link => link.timeframe === 'immediate');
    if (immediateLinks.length > 1) {
      riskFactors.push('Multiple immediate impacts may create volatility');
    }

    // Confidence-based risks
    const lowConfidenceLinks = links.filter(link => link.confidence < 70);
    if (lowConfidenceLinks.length > 0) {
      riskFactors.push(`${lowConfidenceLinks.length} links have below-average confidence`);
    }

    // Sector-specific risks
    riskFactors.push(...profile.riskFactors);

    return riskFactors;
  }

  /**
   * Calculate validation score
   */
  private calculateValidationScore(
    chain: CausalChain,
    validation: { isValid: boolean; criticalFlaws: string[]; strengthFactors: string[] }
  ): number {
    let score = chain.overallConfidence;

    // Penalties for critical flaws
    score -= validation.criticalFlaws.length * 15;

    // Bonuses for strength factors
    score += validation.strengthFactors.length * 10;

    // Bonus for strong overall strength
    if (chain.overallStrength === 'strong') {
      score += 10;
    } else if (chain.overallStrength === 'weak') {
      score -= 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get failed validation result
   */
  private getFailedValidationResult(ticker: string, error: Error): CausalValidationResult {
    return {
      ticker,
      isValid: false,
      causalChain: {
        ticker,
        companyName: 'Unknown',
        newsEvent: 'Validation failed',
        links: [],
        overallConfidence: 0,
        overallStrength: 'weak',
        isValid: false,
        validationReasoning: `Validation failed: ${error.message}`,
        riskFactors: ['Validation error']
      },
      validationScore: 0,
      criticalFlaws: [`Validation error: ${error.message}`],
      strengthFactors: []
    };
  }

  /**
   * Get sector financial sensitivity
   */
  private getSectorFinancialSensitivity(sector: string): { adjustment: number; reasoning: string } {
    const sectorMap: { [key: string]: { adjustment: number; reasoning: string } } = {
      'Financial Services': { adjustment: 10, reasoning: 'Financial sector highly sensitive to economic news' },
      'Technology': { adjustment: 5, reasoning: 'Tech sector moderately sensitive to innovation news' },
      'Healthcare': { adjustment: 8, reasoning: 'Healthcare sensitive to regulatory and clinical news' },
      'Energy': { adjustment: 12, reasoning: 'Energy sector highly sensitive to commodity and policy news' },
      'Consumer': { adjustment: 6, reasoning: 'Consumer sector moderately sensitive to economic conditions' }
    };

    return sectorMap[sector] || { adjustment: 0, reasoning: 'Standard sector sensitivity' };
  }

  /**
   * Get sector market behavior
   */
  private getSectorMarketBehavior(sector: string): { adjustment: number; reasoning: string } {
    const behaviorMap: { [key: string]: { adjustment: number; reasoning: string } } = {
      'Financial Services': { adjustment: 5, reasoning: 'Financial stocks have predictable market reactions' },
      'Technology': { adjustment: -5, reasoning: 'Tech stocks can be volatile and unpredictable' },
      'Healthcare': { adjustment: 8, reasoning: 'Healthcare stocks react strongly to clinical and regulatory news' },
      'Energy': { adjustment: 10, reasoning: 'Energy stocks highly correlated with commodity news' },
      'Consumer': { adjustment: 3, reasoning: 'Consumer stocks have moderate market reactions' }
    };

    return behaviorMap[sector] || { adjustment: 0, reasoning: 'Standard market behavior' };
  }

  /**
   * Determine timeframe for impact
   */
  private determineTimeframe(
    article: GlobalNewsArticle,
    impactType: 'business' | 'financial' | 'market'
  ): 'immediate' | 'short_term' | 'medium_term' | 'long_term' {
    const content = `${article.title} ${article.content}`.toLowerCase();
    
    // Immediate indicators
    if (content.includes('breaking') || content.includes('urgent') || content.includes('emergency')) {
      return 'immediate';
    }

    // Long-term indicators
    if (content.includes('regulation') || content.includes('policy') || content.includes('structural')) {
      return impactType === 'business' ? 'medium_term' : 'short_term';
    }

    // Default timeframes by impact type
    const defaultTimeframes = {
      business: 'short_term' as const,
      financial: 'short_term' as const,
      market: 'immediate' as const
    };

    return defaultTimeframes[impactType];
  }
}
