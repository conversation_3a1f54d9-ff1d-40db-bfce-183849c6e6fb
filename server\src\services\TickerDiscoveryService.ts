import { GlobalNewsService, GlobalNewsArticle } from './GlobalNewsService';
import { NewsImpactAnalysisService, MarketImpactAnalysis } from './NewsImpactAnalysisService';
import { CompanyProfileService, CompanyProfile } from './CompanyProfileService';
import { SemanticSimilarityService } from './SemanticSimilarityService';
import { CausalChainValidationService } from './CausalChainValidationService';
import { MultiModelEnsembleService } from './MultiModelEnsembleService';
import { logger, logDiscovery, logError, logWarn } from '../utils/logger';
import axios from 'axios';
import yahooFinance from 'yahoo-finance2';
import { getWebSocketService } from './WebSocketService';

export interface DiscoveredTicker {
  ticker: string;
  companyName: string;
  sector: string;
  marketCap: number;
  discoveryReason: string;
  impactScore: number;
  confidence: number;
  newsArticles: GlobalNewsArticle[];
  tradingOpportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  discoveredAt: Date;
  // Enhanced validation fields
  semanticSimilarity?: number;
  causalChainStrength?: number;
  ensembleAgreement?: number;
  validationDetails?: {
    semanticRelevance: string;
    causalReasoning: string;
    ensembleConsensus: string;
  };
}

export interface DiscoveryConfig {
  minImpactScore: number;
  minConfidence: number;
  maxTickersPerRun: number;
  excludedSectors: string[];
  minMarketCap: number;
  newsLookbackHours: number;
  analysisCategories: string[];
}

export interface TickerValidation {
  ticker: string;
  isValid: boolean;
  companyName?: string;
  sector?: string;
  marketCap?: number;
  exchange?: string;
  currency?: string;
}

export class TickerDiscoveryService {
  private globalNewsService: GlobalNewsService;
  private newsImpactService: NewsImpactAnalysisService;
  private companyProfileService: CompanyProfileService;
  private semanticSimilarityService: SemanticSimilarityService;
  private causalChainValidationService: CausalChainValidationService;
  private multiModelEnsembleService: MultiModelEnsembleService;
  private discoveryConfig: DiscoveryConfig;

  constructor(
    newsApiKey: string,
    geminiApiKey: string,
    config?: Partial<DiscoveryConfig>
  ) {
    this.globalNewsService = new GlobalNewsService(newsApiKey);
    this.newsImpactService = new NewsImpactAnalysisService(geminiApiKey);
    this.companyProfileService = CompanyProfileService.getInstance();
    this.semanticSimilarityService = SemanticSimilarityService.getInstance(geminiApiKey); // Use Google AI instead of OpenAI
    this.causalChainValidationService = CausalChainValidationService.getInstance();
    this.multiModelEnsembleService = MultiModelEnsembleService.getInstance(geminiApiKey);

    // Suppress verbose notices from yahoo-finance2
    yahooFinance.suppressNotices(['yahooSurvey']);

    this.discoveryConfig = {
      minImpactScore: 50,
      minConfidence: 65,
      maxTickersPerRun: 20,
      excludedSectors: ['Penny Stocks', 'OTC'],
      minMarketCap: *********, // $100M
      newsLookbackHours: 24,
      analysisCategories: ['general', 'world', 'nation', 'technology', 'science', 'health'],
      ...config
    };
  }

  /**
   * Main discovery process - find new tickers from global news
   */
  public async discoverNewTickers(): Promise<DiscoveredTicker[]> {
    const ws = getWebSocketService();
    try {
      logDiscovery('Start', 'Starting ticker discovery process', {
        lookbackHours: this.discoveryConfig.newsLookbackHours,
        categories: this.discoveryConfig.analysisCategories,
      });
      ws.emit('discovery_update', { stage: 'start', message: 'Starting ticker discovery process...' });

      // Step 1: Fetch global news
      ws.emit('discovery_update', { stage: 'news_fetch', message: 'Fetching global news...' });
      const newsArticles = await this.globalNewsService.fetchGlobalNews(
        this.discoveryConfig.analysisCategories,
        this.discoveryConfig.newsLookbackHours,
        100
      );

      // Step 2: Enhanced semantic filtering
      ws.emit('discovery_update', { stage: 'semantic_filter', message: `Found ${newsArticles.length} articles. Applying semantic relevance filtering...` });
      const relevantNews = await this.enhancedNewsFiltering(newsArticles);

      // Step 3: Multi-model ensemble analysis
      ws.emit('discovery_update', { stage: 'ensemble_analysis', message: `Found ${relevantNews.length} relevant articles. Running multi-model analysis...` });
      const analyzedArticles = await this.runEnhancedAnalysis(relevantNews);

      // Step 4: Enhanced ticker extraction with validation
      ws.emit('discovery_update', { stage: 'enhanced_validation', message: 'Running comprehensive ticker validation pipeline...' });
      const discoveredTickers = await this.runEnhancedValidationPipeline(analyzedArticles);

      // Step 7: Filter and rank
      ws.emit('discovery_update', { stage: 'ranking', message: 'Filtering and ranking final tickers...' });
      const filteredTickers = this.filterAndRankTickers(discoveredTickers);

      logDiscovery(
        'Complete',
        `Ticker discovery completed. Found ${discoveredTickers.length} new tickers.`,
        {
          totalNews: newsArticles.length,
          relevantNews: relevantNews.length,
          analyzedArticles: analyzedArticles.length,
          discoveredTickers: discoveredTickers.length,
          finalTickers: discoveredTickers.length,
        }
      );

      ws.emit('discovery_update', { stage: 'complete', message: `Discovery complete. Found ${filteredTickers.length} new tickers.`, data: filteredTickers });

      return filteredTickers.slice(0, this.discoveryConfig.maxTickersPerRun);
    } catch (error) {
      logError('Discovery', 'discoverNewTickers', error as Error);
      ws.emit('discovery_error', { message: 'An error occurred during discovery.', error: (error as Error).message });
      throw error;
    }
  }

  /**
   * Validate ticker symbols using financial API
   */
  private async validateTickers(tickers: string[]): Promise<TickerValidation[]> {
    const validations: TickerValidation[] = [];
    const uniqueTickers = [...new Set(tickers)];

    for (const ticker of uniqueTickers) {
      try {
        // Use Alpha Vantage or similar API to validate ticker
        // For now, using a simple validation approach
        const validation = await this.validateSingleTicker(ticker);
        validations.push(validation);
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        logWarn('Discovery', 'validateTickers', `Failed to validate ticker ${ticker}:`, error as any);
        validations.push({
          ticker,
          isValid: false
        });
      }
    }

    return validations;
  }

  /**
   * Validate a single ticker using Yahoo Finance API
   */
  private async validateSingleTicker(ticker: string): Promise<TickerValidation> {
    try {
      // Simple validation - check if ticker format is valid. Allow for extensions like .B or .A
      if (!/^[A-Z]{1,5}(\.[A-Z])?$/.test(ticker)) {
        return { ticker, isValid: false };
      }

      // Use Yahoo Finance API to validate ticker and get company info
      try {
        const summary = await yahooFinance.quoteSummary(ticker, {
          modules: ['assetProfile', 'price'],
        });

        if (summary && summary.price && summary.price.regularMarketPrice) {
          return {
            ticker,
            isValid: true,
            companyName: summary.price.longName || summary.price.shortName || `${ticker} Corporation`,
            sector: summary.assetProfile?.sector || 'Unknown',
            marketCap: summary.price.marketCap || 0,
            exchange: summary.price.exchangeName || 'Unknown',
            currency: summary.price.currency || 'USD',
          };
        }
      } catch (apiError) {
        // Fallback for tickers that might not have a summary but are valid (e.g. indices)
        if ((apiError as any).code !== '404') {
          try {
            const quote = await yahooFinance.quote(ticker);
            if (quote && quote.regularMarketPrice) {
              return {
                ticker,
                isValid: true,
                companyName: quote.longName || quote.shortName || `${ticker} Corporation`,
                sector: 'N/A',
                marketCap: quote.marketCap || 0,
                exchange: quote.exchange || 'Unknown',
                currency: quote.currency || 'USD',
              };
            }
          } catch (quoteError) {
             logWarn('Discovery', 'validateSingleTicker', `Yahoo Finance fallback quote failed for ${ticker}`, quoteError as any);
          }
        }
      }

      return { ticker, isValid: false };
    } catch (error) {
      logError('Discovery', 'validateSingleTicker', error as Error, { ticker });
      return { ticker, isValid: false };
    }
  }

  /**
   * Create discovered ticker objects
   */
  private async createDiscoveredTickers(
    validatedTickers: TickerValidation[],
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    extractedTickers: { ticker: string; impactScore: number; confidence: number; opportunities: number }[]
  ): Promise<DiscoveredTicker[]> {
    const discoveredTickers: DiscoveredTicker[] = [];

    for (const validation of validatedTickers) {
      if (!validation.isValid) continue;

      const extractedData = extractedTickers.find(t => t.ticker === validation.ticker);
      if (!extractedData) continue;

      // Find related news articles
      const relatedArticles = analyzedArticles.filter(article =>
        article.analysis.affectedCompanies.some(company => company.ticker === validation.ticker) ||
        article.analysis.tradingOpportunities.some(opp => opp.tickers.includes(validation.ticker))
      );

      // Determine discovery reason
      const discoveryReason = this.generateDiscoveryReason(relatedArticles, validation.ticker);

      // Assess risk level
      const riskLevel = this.assessRiskLevel(extractedData.impactScore, extractedData.confidence);

      // Determine timeframe
      const timeframe = this.determineTimeframe(relatedArticles);

      discoveredTickers.push({
        ticker: validation.ticker,
        companyName: validation.companyName || 'Unknown',
        sector: validation.sector || 'Unknown',
        marketCap: validation.marketCap || 0,
        discoveryReason,
        impactScore: extractedData.impactScore,
        confidence: extractedData.confidence,
        newsArticles: relatedArticles.map(a => ({
          title: a.title,
          content: a.content,
          source: a.source,
          publishedAt: a.publishedAt,
          url: a.url,
          category: a.category,
          country: a.country,
          language: a.language,
          relevanceScore: a.relevanceScore
        })),
        tradingOpportunities: extractedData.opportunities,
        riskLevel,
        timeframe,
        discoveredAt: new Date()
      });
    }

    return discoveredTickers;
  }

  /**
   * Filter and rank discovered tickers
   */
  private filterAndRankTickers(tickers: DiscoveredTicker[]): DiscoveredTicker[] {
    return tickers
      .filter(ticker => 
        Math.abs(ticker.impactScore) >= this.discoveryConfig.minImpactScore &&
        ticker.confidence >= this.discoveryConfig.minConfidence &&
        ticker.marketCap >= this.discoveryConfig.minMarketCap &&
        !this.discoveryConfig.excludedSectors.includes(ticker.sector)
      )
      .sort((a, b) => {
        // Sort by weighted score: impact * confidence * opportunities
        const scoreA = Math.abs(a.impactScore) * (a.confidence / 100) * (a.tradingOpportunities + 1);
        const scoreB = Math.abs(b.impactScore) * (b.confidence / 100) * (b.tradingOpportunities + 1);
        return scoreB - scoreA;
      });
  }

  /**
   * Generate discovery reason
   */
  private generateDiscoveryReason(
    articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    ticker: string
  ): string {
    if (articles.length === 0) return 'Unknown discovery reason';

    const reasons = articles.map(article => {
      const companyImpact = article.analysis.affectedCompanies.find(c => c.ticker === ticker);
      if (companyImpact) {
        return `${article.title.substring(0, 100)}... (Impact: ${companyImpact.impactScore})`;
      }

      const opportunity = article.analysis.tradingOpportunities.find(opp => opp.tickers.includes(ticker));
      if (opportunity) {
        return `Trading opportunity: ${opportunity.type} (${article.title.substring(0, 80)}...)`;
      }

      return article.title.substring(0, 100) + '...';
    });

    return reasons[0]; // Return the first (most relevant) reason
  }

  /**
   * Assess risk level
   */
  private assessRiskLevel(impactScore: number, confidence: number): 'low' | 'medium' | 'high' {
    const riskScore = Math.abs(impactScore) * (1 - confidence / 100);
    
    if (riskScore < 20) return 'low';
    if (riskScore < 50) return 'medium';
    return 'high';
  }

  /**
   * Determine timeframe
   */
  private determineTimeframe(articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]): string {
    if (articles.length === 0) return 'short_term';

    const timeframes = articles.map(a => a.analysis.timeframe);
    const timeframeCounts = timeframes.reduce((acc, tf) => {
      acc[tf] = (acc[tf] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    return Object.keys(timeframeCounts).reduce((a, b) => 
      timeframeCounts[a] > timeframeCounts[b] ? a : b
    );
  }

  /**
   * Get discovery statistics from database
   */
  public async getDiscoveryStats(): Promise<{
    totalNewsAnalyzed: number;
    tickersDiscovered: number;
    averageConfidence: number;
    topSectors: string[];
    riskDistribution: { [key: string]: number };
  }> {
    try {
      // Import models dynamically to avoid circular dependencies
      const { DiscoveredTickerModel } = await import('../models/DiscoveredTicker');
      const { LogEntryModel } = await import('../models/LogEntry');

      // Get total news analyzed from logs
      const newsAnalyzedLogs = await LogEntryModel.countDocuments({
        type: 'news_analysis',
        timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
      });

      // Get discovered tickers
      const discoveredTickers = await DiscoveredTickerModel.find({
        discoveredAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
      });

      // Calculate average confidence
      const totalConfidence = discoveredTickers.reduce((sum, ticker) => sum + ticker.confidence, 0);
      const averageConfidence = discoveredTickers.length > 0 ? Math.round(totalConfidence / discoveredTickers.length) : 0;

      // Get top sectors
      const sectorCounts: { [key: string]: number } = {};
      discoveredTickers.forEach(ticker => {
        sectorCounts[ticker.sector] = (sectorCounts[ticker.sector] || 0) + 1;
      });
      const topSectors = Object.entries(sectorCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 4)
        .map(([sector]) => sector);

      // Get risk distribution
      const riskDistribution = { low: 0, medium: 0, high: 0 };
      discoveredTickers.forEach(ticker => {
        riskDistribution[ticker.riskLevel]++;
      });

      return {
        totalNewsAnalyzed: newsAnalyzedLogs || 0,
        tickersDiscovered: discoveredTickers.length,
        averageConfidence,
        topSectors: topSectors.length > 0 ? topSectors : ['Technology', 'Healthcare'],
        riskDistribution
      };
    } catch (error) {
      logger.error('Error fetching discovery stats:', error);
      // Return default stats if database query fails
      return {
        totalNewsAnalyzed: 0,
        tickersDiscovered: 0,
        averageConfidence: 0,
        topSectors: ['Technology', 'Healthcare'],
        riskDistribution: { low: 0, medium: 0, high: 0 }
      };
    }
  }

  /**
   * Enhanced news filtering using semantic similarity
   */
  private async enhancedNewsFiltering(articles: GlobalNewsArticle[]): Promise<GlobalNewsArticle[]> {
    try {
      // First apply traditional filtering
      const traditionallyFiltered = this.globalNewsService.filterMarketRelevantNews(articles);

      // Then apply semantic filtering for better relevance
      const semanticallyFiltered: GlobalNewsArticle[] = [];

      for (const article of traditionallyFiltered) {
        // Check semantic relevance to financial markets
        const marketRelevance = await this.calculateMarketSemanticRelevance(article);

        if (marketRelevance > 0.01) { // Very low threshold for market relevance
          semanticallyFiltered.push({
            ...article,
            relevanceScore: Math.max(article.relevanceScore, marketRelevance)
          });
        }
      }

      logDiscovery('SemanticFilter', `Filtered ${articles.length} → ${traditionallyFiltered.length} → ${semanticallyFiltered.length} articles`, {
        originalCount: articles.length,
        traditionalFiltered: traditionallyFiltered.length,
        semanticallyFiltered: semanticallyFiltered.length
      });

      return semanticallyFiltered.sort((a, b) => b.relevanceScore - a.relevanceScore);
    } catch (error) {
      logError('Discovery', 'enhancedNewsFiltering', error as Error);
      // Fallback to traditional filtering
      return this.globalNewsService.filterMarketRelevantNews(articles);
    }
  }

  /**
   * Calculate semantic relevance to financial markets
   */
  private async calculateMarketSemanticRelevance(article: GlobalNewsArticle): Promise<number> {
    const content = `${article.title} ${article.content}`.toLowerCase();
    let relevance = 0.5; // High base relevance for all articles to be more permissive

    // High-impact financial keywords (0.4 each)
    const highImpactKeywords = [
      'market', 'economy', 'financial', 'stock', 'investment', 'earnings', 'revenue',
      'profit', 'merger', 'acquisition', 'ipo', 'bankruptcy', 'recession', 'trading',
      'shares', 'dividend', 'valuation', 'capital', 'funding'
    ];

    // Medium-impact business keywords (0.3 each)
    const mediumImpactKeywords = [
      'business', 'company', 'corporate', 'industry', 'sector', 'growth', 'sales',
      'regulation', 'policy', 'trade', 'supply chain', 'inflation', 'interest rate',
      'ceo', 'executive', 'board', 'strategy', 'partnership', 'contract'
    ];

    // Low-impact general keywords (0.2 each)
    const lowImpactKeywords = [
      'government', 'federal', 'technology', 'innovation', 'energy', 'healthcare',
      'manufacturing', 'retail', 'consumer', 'employment', 'jobs', 'workforce',
      'startup', 'launch', 'product', 'service', 'customer', 'market share',
      'news', 'report', 'announcement', 'update', 'development', 'change',
      'decision', 'plan', 'project', 'deal', 'agreement', 'investment',
      'price', 'cost', 'budget', 'spending', 'economic', 'finance'
    ];

    // Check high-impact keywords
    for (const keyword of highImpactKeywords) {
      if (content.includes(keyword)) {
        relevance += 0.4;
      }
    }

    // Check medium-impact keywords
    for (const keyword of mediumImpactKeywords) {
      if (content.includes(keyword)) {
        relevance += 0.3;
      }
    }

    // Check low-impact keywords
    for (const keyword of lowImpactKeywords) {
      if (content.includes(keyword)) {
        relevance += 0.2;
      }
    }

    return Math.min(1.0, relevance);
  }

  /**
   * Run enhanced analysis with multiple models
   */
  private async runEnhancedAnalysis(articles: GlobalNewsArticle[]): Promise<any[]> {
    const results: any[] = [];

    for (const article of articles) {
      try {
        // Get company profiles for context
        const relevantProfiles = await this.getRelevantCompanyProfiles(article);

        // Run ensemble analysis
        const ensembleResult = await this.multiModelEnsembleService.analyzeWithEnsemble(
          article,
          relevantProfiles
        );

        results.push({
          ...article,
          analysis: ensembleResult.primaryAnalysis,
          ensembleMetrics: {
            consensusScore: ensembleResult.consensusScore,
            agreementLevel: ensembleResult.agreementLevel,
            modelResults: ensembleResult.modelResults.length,
            processingTime: ensembleResult.processingTime
          }
        });

        logDiscovery('EnsembleAnalysis', `Analyzed article: ${article.title.substring(0, 50)}...`, {
          consensusScore: ensembleResult.consensusScore,
          agreementLevel: ensembleResult.agreementLevel,
          affectedCompanies: ensembleResult.primaryAnalysis.affectedCompanies.length
        });

      } catch (error) {
        logError('Discovery', 'runEnhancedAnalysis', error as Error, { articleTitle: article.title });

        // Fallback to single model analysis
        const fallbackAnalysis = await this.newsImpactService.analyzeMarketImpact(article);
        results.push({
          ...article,
          analysis: fallbackAnalysis,
          ensembleMetrics: {
            consensusScore: 0,
            agreementLevel: 'low',
            modelResults: 1,
            processingTime: 0
          }
        });
      }
    }

    return results;
  }

  /**
   * Get relevant company profiles for analysis context
   */
  private async getRelevantCompanyProfiles(article: GlobalNewsArticle): Promise<CompanyProfile[]> {
    // For now, return a subset of known profiles
    // In production, this could be more sophisticated
    const knownTickers = ['NAVI', 'ZOOM', 'AAPL', 'UBER', 'F'];
    const profiles: CompanyProfile[] = [];

    for (const ticker of knownTickers) {
      try {
        const profile = await this.companyProfileService.getProfile(ticker);
        if (profile) {
          profiles.push(profile);
        }
      } catch (error) {
        // Continue with other profiles
      }
    }

    return profiles.slice(0, 10); // Limit to avoid token limits
  }

  /**
   * Run comprehensive validation pipeline
   */
  private async runEnhancedValidationPipeline(analyzedArticles: any[]): Promise<DiscoveredTicker[]> {
    const discoveredTickers: DiscoveredTicker[] = [];

    for (const article of analyzedArticles) {
      for (const companyImpact of article.analysis.affectedCompanies) {
        if (!companyImpact.ticker) continue;

        try {
          // Step 1: Get company profile
          const profile = await this.companyProfileService.getProfile(companyImpact.ticker);
          if (!profile) continue;

          // Step 2: Validate ticker exists and is tradeable
          const tickerValidation = await this.validateSingleTicker(companyImpact.ticker);
          if (!tickerValidation.isValid) continue;

          // Step 3: Semantic similarity validation
          const semanticResult = await this.semanticSimilarityService.calculateNewsSimilarity(article, profile);

          // Step 4: Causal chain validation
          const causalResult = await this.causalChainValidationService.validateCausalChain(
            article,
            profile,
            companyImpact
          );

          // Step 5: Overall validation decision
          const validationDecision = this.makeValidationDecision(
            semanticResult,
            causalResult,
            companyImpact
          );

          if (validationDecision.isValid) {
            const discoveredTicker = await this.createEnhancedDiscoveredTicker(
              article,
              profile,
              companyImpact,
              tickerValidation,
              semanticResult,
              causalResult,
              validationDecision
            );

            discoveredTickers.push(discoveredTicker);

            logDiscovery('TickerValidated', `Validated ticker: ${companyImpact.ticker}`, {
              ticker: companyImpact.ticker,
              semanticSimilarity: semanticResult.similarity,
              causalStrength: causalResult.validationScore,
              overallConfidence: validationDecision.confidence
            });
          } else {
            logWarn('Discovery', 'TickerRejected', `Rejected ticker ${companyImpact.ticker}: ${validationDecision.reason}`);
          }

        } catch (error) {
          logError('Discovery', 'runEnhancedValidationPipeline', error as Error, {
            ticker: companyImpact.ticker,
            articleTitle: article.title
          });
        }
      }
    }

    return discoveredTickers;
  }

  /**
   * Make validation decision based on all validation results
   */
  private makeValidationDecision(
    semanticResult: any,
    causalResult: any,
    companyImpact: any
  ): { isValid: boolean; confidence: number; reason: string } {
    let confidence = companyImpact.confidence;
    const issues: string[] = [];
    const strengths: string[] = [];

    // Semantic similarity check - adjusted for geopolitical events
    if (semanticResult.similarity < 0.15) {
      confidence -= 20; // Reduced penalty
      issues.push('Low semantic similarity');
    } else if (semanticResult.similarity > 0.4) { // Lowered threshold for "high"
      confidence += 10;
      strengths.push('High semantic similarity');
    }

    // Causal chain validation
    if (!causalResult.isValid) {
      confidence -= 40;
      issues.push('Invalid causal chain');
    } else if (causalResult.validationScore > 80) {
      confidence += 15;
      strengths.push('Strong causal chain');
    }

    // Minimum thresholds - significantly reduced for geopolitical events
    const isValid = confidence > 30 &&
                   causalResult.isValid &&
                   semanticResult.similarity > 0.1;

    const reason = isValid ?
      `Validated: ${strengths.join(', ')}` :
      `Rejected: ${issues.join(', ')}`;

    return {
      isValid,
      confidence: Math.max(0, Math.min(100, confidence)),
      reason
    };
  }

  /**
   * Create enhanced discovered ticker with validation details
   */
  private async createEnhancedDiscoveredTicker(
    article: any,
    profile: CompanyProfile,
    companyImpact: any,
    tickerValidation: any,
    semanticResult: any,
    causalResult: any,
    validationDecision: any
  ): Promise<DiscoveredTicker> {
    const riskLevel = this.assessRiskLevel(companyImpact.impactScore, validationDecision.confidence);
    const timeframe = this.determineTimeframe([article]);

    return {
      ticker: profile.ticker,
      companyName: profile.companyName,
      sector: profile.sector,
      marketCap: profile.marketCap || tickerValidation.marketCap || 0,
      discoveryReason: this.generateEnhancedDiscoveryReason(article, companyImpact, semanticResult, causalResult),
      impactScore: companyImpact.impactScore,
      confidence: validationDecision.confidence,
      newsArticles: [{
        title: article.title,
        content: article.content,
        source: article.source,
        publishedAt: article.publishedAt,
        url: article.url,
        category: article.category,
        country: article.country,
        language: article.language,
        relevanceScore: article.relevanceScore
      }],
      tradingOpportunities: article.analysis.tradingOpportunities.filter((opp: any) =>
        opp.tickers.includes(profile.ticker)
      ).length,
      riskLevel,
      timeframe,
      discoveredAt: new Date(),
      // Enhanced validation fields
      semanticSimilarity: semanticResult.similarity,
      causalChainStrength: causalResult.validationScore / 100,
      ensembleAgreement: article.ensembleMetrics?.consensusScore / 100 || 0,
      validationDetails: {
        semanticRelevance: semanticResult.reasoning,
        causalReasoning: causalResult.causalChain.validationReasoning,
        ensembleConsensus: `${article.ensembleMetrics?.agreementLevel || 'unknown'} agreement (${article.ensembleMetrics?.consensusScore || 0}%)`
      }
    };
  }

  /**
   * Generate enhanced discovery reason with validation context
   */
  private generateEnhancedDiscoveryReason(
    article: any,
    companyImpact: any,
    semanticResult: any,
    causalResult: any
  ): string {
    const reasons: string[] = [];

    // Primary reason from impact
    reasons.push(`${article.title.substring(0, 80)}... (Impact: ${companyImpact.impactScore})`);

    // Validation strengths - adjusted thresholds
    if (semanticResult.similarity > 0.4) { // Lowered threshold
      reasons.push(`High semantic relevance (${Math.round(semanticResult.similarity * 100)}%)`);
    }

    if (causalResult.validationScore > 80) {
      reasons.push(`Strong causal chain (${causalResult.validationScore}%)`);
    }

    // Causal mechanism
    if (causalResult.causalChain.links.length > 0) {
      const primaryMechanism = causalResult.causalChain.links[0].mechanism;
      reasons.push(`Mechanism: ${primaryMechanism.substring(0, 50)}...`);
    }

    return reasons.join(' | ');
  }
}
