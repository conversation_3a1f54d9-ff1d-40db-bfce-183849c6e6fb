// Debug the AI service in the server environment
const fs = require('fs');
const path = require('path');

// Load environment variables
try {
  const envPath = path.join(__dirname, 'server', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  }
} catch (error) {
  console.log('Could not load .env file');
}

// Mock logger
global.logger = {
  info: (...args) => console.log('[INFO]', ...args),
  warn: (...args) => console.warn('[WARN]', ...args),
  error: (...args) => console.error('[ERROR]', ...args)
};

async function debugServerAI() {
  console.log('🔧 Debugging AI Service in Server Environment');
  console.log('=' .repeat(60));

  try {
    // Import the compiled services
    const { NewsImpactAnalysisService } = require('./server/dist/services/NewsImpactAnalysisService');
    const { GeopoliticalAnalysisService } = require('./server/dist/services/GeopoliticalAnalysisService');

    const apiKey = process.env.GOOGLE_AI_API_KEY;
    console.log(`✅ API Key available: ${apiKey ? 'Yes' : 'No'}`);
    
    if (!apiKey) {
      console.log('❌ No API key found, cannot proceed');
      return;
    }

    // Test article
    const testArticle = {
      title: "Iran accuses US of 'waging war' under 'absurd pretext'",
      content: "Iran's representative to the UN Security Council has accused the US of having waged a war against Iran under an absurd pretext following recent military actions in the region. The Iranian government has threatened to retaliate against what it calls unprovoked aggression. Oil markets are watching closely as tensions escalate in the Middle East.",
      source: "BBC News",
      publishedAt: new Date(),
      category: "general",
      url: "https://example.com/iran-conflict"
    };

    console.log('\n🌍 Testing GeopoliticalAnalysisService...');
    const geoService = GeopoliticalAnalysisService.getInstance();
    const geoAnalysis = geoService.analyzeGeopoliticalEvent(testArticle);
    
    if (geoAnalysis) {
      console.log('✅ Geopolitical analysis successful');
      console.log(`   Event Type: ${geoAnalysis.event.type}`);
      console.log(`   Sectors: ${geoAnalysis.sectorMappings.length}`);
      console.log(`   Tickers: ${geoAnalysis.sectorMappings.reduce((sum, s) => sum + s.affectedTickers.length, 0)}`);
    } else {
      console.log('❌ Geopolitical analysis failed');
    }

    console.log('\n🤖 Testing NewsImpactAnalysisService...');
    const newsService = new NewsImpactAnalysisService(apiKey);
    
    console.log('   Calling analyzeMarketImpact...');
    const analysis = await newsService.analyzeMarketImpact(testArticle);
    
    console.log('\n📊 Analysis Results:');
    console.log(`   Impact Score: ${analysis.impactScore}`);
    console.log(`   Confidence: ${analysis.confidence}`);
    console.log(`   Affected Sectors: ${analysis.affectedSectors.length}`);
    console.log(`   Affected Companies: ${analysis.affectedCompanies.length}`);
    console.log(`   Reasoning: ${analysis.reasoning.substring(0, 100)}...`);

    if (analysis.confidence === 0) {
      console.log('\n❌ ISSUE: Analysis returned fallback response');
      console.log('   This indicates the AI model is not responding correctly');
    } else {
      console.log('\n✅ SUCCESS: Analysis completed successfully');
      if (analysis.affectedCompanies.length > 0) {
        console.log('   Affected Companies:');
        analysis.affectedCompanies.forEach(company => {
          console.log(`     • ${company.ticker}: ${company.companyName}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the debug
debugServerAI();
