const axios = require('axios');

// Test the redesigned AI discovery system with geopolitical scenarios
async function testGeopoliticalDiscovery() {
  console.log('🔍 Testing Redesigned AI Discovery System for Geopolitical Events');
  console.log('=' .repeat(70));

  const baseUrl = 'http://localhost:3001/api/discovery';
  
  try {
    // Test 1: Iran-Israel Conflict Scenario
    console.log('\n📰 Test 1: Iran-Israel Conflict Analysis');
    console.log('-'.repeat(50));
    
    const iranIsraelArticle = {
      title: "Iran launches missile strikes against Israeli military targets amid escalating Middle East tensions",
      content: "Iran has launched a series of ballistic missile strikes targeting Israeli military installations in response to recent airstrikes. The attacks have raised concerns about oil supply disruptions through the Strait of Hormuz, with crude oil prices surging 8% in early trading. Defense analysts warn of potential escalation that could affect global energy markets and increase demand for military equipment. Regional shipping routes are being rerouted as tensions mount.",
      source: "Reuters",
      publishedAt: new Date().toISOString(),
      category: "world",
      url: "https://example.com/iran-israel-conflict"
    };

    // Test the market impact analysis
    const response1 = await axios.post(`${baseUrl}/analyze`, {
      articles: [iranIsraelArticle]
    });

    console.log('✅ Analysis completed successfully');
    console.log(`📊 Articles analyzed: ${response1.data.data.summary.analyzedArticles}`);
    console.log(`🎯 Unique tickers found: ${response1.data.data.summary.uniqueTickers}`);
    console.log(`🏭 Affected sectors: ${response1.data.data.summary.affectedSectors}`);
    console.log(`💼 Trading opportunities: ${response1.data.data.summary.tradingOpportunities}`);

    if (response1.data.data.insights.extractedTickers.length > 0) {
      console.log('\n🎯 Discovered Tickers:');
      response1.data.data.insights.extractedTickers.slice(0, 10).forEach(ticker => {
        console.log(`  • ${ticker.ticker}: Impact ${ticker.impactScore.toFixed(1)}, Confidence ${ticker.confidence.toFixed(1)}%`);
      });
    }

    if (Object.keys(response1.data.data.insights.sectorSummary).length > 0) {
      console.log('\n🏭 Sector Impact Summary:');
      Object.entries(response1.data.data.insights.sectorSummary).forEach(([sector, data]) => {
        console.log(`  • ${sector}: Impact ${data.totalImpact.toFixed(1)}, Confidence ${data.avgConfidence.toFixed(1)}%`);
      });
    }

    // Test 2: Strait of Hormuz Threat Scenario
    console.log('\n\n📰 Test 2: Strait of Hormuz Energy Infrastructure Threat');
    console.log('-'.repeat(50));
    
    const hormuzArticle = {
      title: "Iran threatens to close Strait of Hormuz amid rising tensions, oil prices spike",
      content: "Iranian officials have threatened to close the strategic Strait of Hormuz shipping lane, through which 20% of global oil passes daily. The threat comes as geopolitical tensions escalate in the region. Energy companies are assessing potential supply disruptions, while shipping companies are preparing alternative routes. Oil futures jumped 12% on the news, with analysts warning of potential $150 per barrel crude if the strait is actually blocked. Defense contractors are seeing increased interest in naval protection systems.",
      source: "Bloomberg",
      publishedAt: new Date().toISOString(),
      category: "business",
      url: "https://example.com/strait-hormuz-threat"
    };

    const response2 = await axios.post(`${baseUrl}/analyze`, {
      articles: [hormuzArticle]
    });

    console.log('✅ Analysis completed successfully');
    console.log(`📊 Articles analyzed: ${response2.data.data.summary.analyzedArticles}`);
    console.log(`🎯 Unique tickers found: ${response2.data.data.summary.uniqueTickers}`);
    console.log(`🏭 Affected sectors: ${response2.data.data.summary.affectedSectors}`);

    if (response2.data.data.insights.extractedTickers.length > 0) {
      console.log('\n🎯 Discovered Tickers:');
      response2.data.data.insights.extractedTickers.slice(0, 10).forEach(ticker => {
        console.log(`  • ${ticker.ticker}: Impact ${ticker.impactScore.toFixed(1)}, Confidence ${ticker.confidence.toFixed(1)}%`);
      });
    }

    // Test 3: Military Defense Spending Scenario
    console.log('\n\n📰 Test 3: Military Defense Spending Analysis');
    console.log('-'.repeat(50));
    
    const defenseArticle = {
      title: "US announces $50 billion defense package amid global conflicts, defense contractors surge",
      content: "The United States has announced a massive $50 billion defense spending package in response to escalating global conflicts. The package includes funding for advanced missile defense systems, fighter jets, and naval vessels. Major defense contractors are expected to benefit significantly from the increased spending. Lockheed Martin, Raytheon, and Northrop Grumman are among the companies likely to receive substantial contracts. The announcement comes as NATO allies also increase their defense budgets in response to regional threats.",
      source: "Defense News",
      publishedAt: new Date().toISOString(),
      category: "politics",
      url: "https://example.com/defense-spending"
    };

    const response3 = await axios.post(`${baseUrl}/analyze`, {
      articles: [defenseArticle]
    });

    console.log('✅ Analysis completed successfully');
    console.log(`📊 Articles analyzed: ${response3.data.data.summary.analyzedArticles}`);
    console.log(`🎯 Unique tickers found: ${response3.data.data.summary.uniqueTickers}`);
    console.log(`🏭 Affected sectors: ${response3.data.data.summary.affectedSectors}`);

    if (response3.data.data.insights.extractedTickers.length > 0) {
      console.log('\n🎯 Discovered Tickers:');
      response3.data.data.insights.extractedTickers.slice(0, 10).forEach(ticker => {
        console.log(`  • ${ticker.ticker}: Impact ${ticker.impactScore.toFixed(1)}, Confidence ${ticker.confidence.toFixed(1)}%`);
      });
    }

    console.log('\n' + '='.repeat(70));
    console.log('🎉 Geopolitical Discovery System Test Completed Successfully!');
    console.log('✅ The redesigned system should now properly identify:');
    console.log('   • Energy companies affected by geopolitical conflicts');
    console.log('   • Defense contractors benefiting from military spending');
    console.log('   • Shipping companies impacted by supply route disruptions');
    console.log('   • Detailed cause-and-effect reasoning chains');
    console.log('   • Appropriate confidence scores for indirect impacts');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testGeopoliticalDiscovery();
