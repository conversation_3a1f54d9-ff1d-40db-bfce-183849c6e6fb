const { AIRateLimiter } = require('./server/dist/utils/rateLimiter');
const { NewsImpactAnalysisService } = require('./server/dist/services/NewsImpactAnalysisService');
const { SETTINGS } = require('./server/dist/config/settings');

// Dummy API key (replace with real if needed)
const apiKey = process.env.GOOGLE_AI_API_KEY || 'AIzaSyAmjNgzkwDL39syGledc7qyjSJ_zQwKD9c';

// Dummy articles for testing
const articles = [
  {
    title: 'Test Article 1',
    content: 'This is a test content about oil prices rising due to conflict.',
    source: 'Reuters',
    publishedAt: new Date().toISOString(),
    category: 'energy'
  },
  {
    title: 'Test Article 2',
    content: 'Defense stocks surge after new military contracts announced.',
    source: 'Bloomberg',
    publishedAt: new Date().toISOString(),
    category: 'defense'
  }
];

async function main() {
  console.log('--- LLM Single-Article Rate Limit Test ---');
  const limiter = AIRateLimiter.getInstance(SETTINGS.aiRateLimits[SETTINGS.llmModel] || SETTINGS.aiRateLimits.gemini2FlashLite);
  const statsBefore = limiter.getUsageStats(SETTINGS.llmModel);
  console.log('Rate limiter stats BEFORE:', statsBefore);

  // Create the service (will fail if no real API key, but that's OK for structure test)
  const service = new NewsImpactAnalysisService(apiKey);

  for (let i = 0; i < articles.length; i++) {
    const article = articles[i];
    console.log(`\n--- Analyzing Article ${i + 1} ---`);
    try {
      const result = await service.analyzeMarketImpact(article);
      console.log(`LLM result for Article ${i + 1}:`, result);
    } catch (err) {
      console.error(`Error during analyzeMarketImpact for Article ${i + 1}:`, err);
    }
  }

  const statsAfter = limiter.getUsageStats(SETTINGS.llmModel);
  console.log('\nRate limiter stats AFTER:', statsAfter);
}

main(); 