// Test pattern matching for geopolitical events
const testArticles = [
  {
    title: "Iran accuses US of 'waging war' under 'absurd pretext'",
    content: "Iran has accused the United States of waging war under an absurd pretext following recent military actions in the region. The Iranian government has threatened to retaliate against what it calls unprovoked aggression. Oil markets are watching closely as tensions escalate in the Middle East."
  },
  {
    title: "U.S. calls on China to prevent Iran from closing Strait of Hormuz",
    content: "The United States has called on China to use its influence to prevent Iran from closing the strategic Strait of Hormuz shipping lane. The strait is a critical chokepoint for global oil supplies, with approximately 20% of the world's petroleum passing through daily."
  },
  {
    title: "The US military used a 30,000-pound bunker-buster bomb in Middle East operation",
    content: "The United States military deployed a massive 30,000-pound bunker-buster bomb in a recent Middle East operation. The GBU-57 Massive Ordnance Penetrator is designed to destroy deeply buried targets and represents a significant escalation in military capabilities."
  }
];

// Test pattern matching logic
function testPatternMatching() {
  console.log('🔍 Testing Pattern Matching Logic');
  console.log('=' .repeat(50));

  const conflictPatterns = [
    'iran.*israel', 'israel.*iran', 'iran.*strike', 'israel.*strike', 'iran.*attack', 'israel.*attack',
    'middle east.*conflict', 'gaza.*war', 'lebanon.*strike', 'iran.*military', 'israel.*military',
    'russia.*ukraine', 'ukraine.*russia', 'nato.*conflict', 'military.*strike', 'us.*strike',
    'missile.*attack', 'drone.*strike', 'air.*strike', 'bombing', 'invasion', 'bunker.*buster',
    'iran.*hostage', 'israel.*hostage', 'iran.*nuclear', 'israel.*nuclear', 'iran.*bomb', 'israel.*bomb',
    'iran.*war', 'iran.*waging', 'iran.*accuses'
  ];

  const energyPatterns = [
    'strait.*hormuz', 'hormuz', 'oil.*supply', 'gas.*pipeline', 'energy.*infrastructure',
    'oil.*price', 'crude.*oil', 'natural.*gas', 'lng', 'opec', 'petroleum',
    'iran.*oil', 'iran.*gas', 'iran.*energy', 'iran.*close', 'iran.*shut', 'iran.*block',
    'energy.*threat', 'oil.*threat', 'supply.*disruption', 'energy.*security'
  ];

  const militaryPatterns = [
    'military.*operation', 'defense.*spending', 'arms.*deal', 'weapon.*system',
    'fighter.*jet', 'missile.*defense', 'naval.*fleet', 'army.*deployment',
    'bunker.*buster', 'b-2.*bomber', 'military.*used', 'us.*military', 'bomber',
    'defense.*contract', 'military.*equipment', 'weapon.*delivery', 'arms.*sale'
  ];

  function matchesPatterns(content, patterns) {
    return patterns.some(pattern => new RegExp(pattern, 'i').test(content));
  }

  testArticles.forEach((article, index) => {
    console.log(`\n📰 Test ${index + 1}: ${article.title.substring(0, 60)}...`);
    console.log('-'.repeat(40));
    
    const content = `${article.title} ${article.content}`.toLowerCase();
    console.log(`Content preview: ${content.substring(0, 150)}...`);
    
    // Test conflict patterns
    const conflictMatch = matchesPatterns(content, conflictPatterns);
    if (conflictMatch) {
      const matchedPatterns = conflictPatterns.filter(pattern => new RegExp(pattern, 'i').test(content));
      console.log(`✅ CONFLICT patterns matched: ${matchedPatterns.join(', ')}`);
    }
    
    // Test energy patterns
    const energyMatch = matchesPatterns(content, energyPatterns);
    if (energyMatch) {
      const matchedPatterns = energyPatterns.filter(pattern => new RegExp(pattern, 'i').test(content));
      console.log(`✅ ENERGY patterns matched: ${matchedPatterns.join(', ')}`);
    }
    
    // Test military patterns
    const militaryMatch = matchesPatterns(content, militaryPatterns);
    if (militaryMatch) {
      const matchedPatterns = militaryPatterns.filter(pattern => new RegExp(pattern, 'i').test(content));
      console.log(`✅ MILITARY patterns matched: ${matchedPatterns.join(', ')}`);
    }
    
    if (!conflictMatch && !energyMatch && !militaryMatch) {
      console.log('❌ No patterns matched');
    }
  });
  
  console.log('\n' + '='.repeat(50));
  console.log('🎯 Pattern Matching Test Complete!');
}

// Run the test
testPatternMatching();
